package com.erroranalysis.app.ui.study

/**
 * 卡片篩選條件
 */
data class CardFilter(
    val keyword: String = "",
    val selectedMasteryLevels: Set<CardMastery> = emptySet(),
    val selectedTags: Set<String> = emptySet()
) {
    /**
     * 檢查是否有任何篩選條件
     */
    fun hasFilter(): Boolean {
        return keyword.isNotEmpty() || 
               selectedMasteryLevels.isNotEmpty() || 
               selectedTags.isNotEmpty()
    }
    
    /**
     * 檢查卡片是否符合篩選條件
     */
    fun matches(card: StudyCard): Boolean {
        // 關鍵字篩選
        if (keyword.isNotEmpty()) {
            val keywordLower = keyword.lowercase()
            val questionText = extractTextFromContent(card.question).lowercase()
            val answerText = extractTextFromContent(card.answer).lowercase()
            
            if (!questionText.contains(keywordLower) && !answerText.contains(keywordLower)) {
                return false
            }
        }
        
        // 熟練度篩選
        if (selectedMasteryLevels.isNotEmpty()) {
            if (!selectedMasteryLevels.contains(card.mastery)) {
                return false
            }
        }
        
        // 標籤篩選
        if (selectedTags.isNotEmpty()) {
            if (card.tags.none { it in selectedTags }) {
                return false
            }
        }
        
        return true
    }
    
    /**
     * 從JSON內容中提取純文字
     */
    private fun extractTextFromContent(content: String): String {
        return try {
            val jsonArray = org.json.JSONArray(content)
            val textBuilder = StringBuilder()
            
            for (i in 0 until jsonArray.length()) {
                val item = jsonArray.getJSONObject(i)
                if (item.getString("type") == "text") {
                    textBuilder.append(item.getString("content")).append(" ")
                }
            }
            
            textBuilder.toString().trim()
        } catch (e: Exception) {
            // 如果不是JSON格式，直接返回原文
            content
        }
    }
}

/**
 * 篩選結果統計
 */
data class FilterResult(
    val totalCards: Int,
    val filteredCards: Int,
    val masteryDistribution: Map<CardMastery, Int>,
    val tagDistribution: Map<String, Int>
)
