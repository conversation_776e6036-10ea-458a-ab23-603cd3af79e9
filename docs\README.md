# 📋 錯題庫應用開發完整文檔

## 🎯 項目概述

### 應用名稱
**ErrorAnalysisApp** - 錯題庫應用

### 核心功能
- 📚 **卡組管理**：創建、編輯、刪除卡組
- 📝 **卡片管理**：支援圖文混合的題目和答案
- 🖼️ **圖片功能**：拍照、從相冊選擇、圖片縮放
- 📱 **檢視模式**：專用的卡片檢視界面
- ✏️ **編輯模式**：完整的卡片編輯功能

### 技術架構
- **語言**：Kotlin
- **最低SDK**：API 24 (Android 7.0)
- **目標SDK**：API 34
- **架構**：MVVM + Repository Pattern
- **數據存儲**：SQLite + Room
- **圖片處理**：自定義ImageSpan + Bitmap

## 🏗️ 項目結構

```
app/src/main/java/com/erroranalysis/app/
├── data/                          # 數據層
│   ├── database/                  # 數據庫相關
│   │   ├── AppDatabase.kt         # Room數據庫
│   │   ├── entities/              # 數據實體
│   │   │   ├── Deck.kt           # 卡組實體
│   │   │   └── Card.kt           # 卡片實體
│   │   └── dao/                   # 數據訪問對象
│   │       ├── DeckDao.kt        # 卡組DAO
│   │       └── CardDao.kt        # 卡片DAO
│   ├── DeckDataManager.kt         # 數據管理器
│   └── models/                    # 數據模型
│       ├── SimpleDeck.kt         # 簡化卡組模型
│       ├── StudyCard.kt          # 學習卡片模型
│       └── Difficulty.kt         # 難度枚舉
├── ui/                           # UI層
│   ├── main/                     # 主界面
│   │   └── MainActivity.kt       # 主Activity
│   ├── study/                    # 學習模塊
│   │   ├── StudyActivity.kt      # 卡組列表
│   │   ├── DeckDetailActivity.kt # 卡組詳情
│   │   ├── CardEditActivity.kt   # 卡片編輯
│   │   ├── CardViewActivity.kt   # 卡片檢視
│   │   └── adapters/             # 適配器
│   │       ├── DeckAdapter.kt    # 卡組適配器
│   │       └── StudyCardAdapter.kt # 卡片適配器
│   └── widgets/                  # 自定義組件
│       └── RichTextEditText.kt   # 圖文混合編輯器
├── utils/                        # 工具類
│   ├── ImageStorageManager.kt    # 圖片存儲管理
│   └── PermissionHelper.kt       # 權限幫助類
└── MainActivity.kt               # 應用入口
```

## 📊 數據模型

### 1. Deck（卡組）
```kotlin
@Entity(tableName = "decks")
data class Deck(
    @PrimaryKey val id: String = UUID.randomUUID().toString(),
    val name: String,
    val description: String = "",
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val cardCount: Int = 0
)
```

### 2. Card（卡片）
```kotlin
@Entity(tableName = "cards")
data class Card(
    @PrimaryKey val id: String = UUID.randomUUID().toString(),
    val deckId: String,
    val question: String,        // JSON格式的圖文混合內容
    val answer: String,          // JSON格式的圖文混合內容
    val tags: List<String> = emptyList(),
    val difficulty: Difficulty = Difficulty.MEDIUM,
    val masteryLevel: Int = 0,   // 0-5
    val reviewCount: Int = 0,
    val accuracyRate: Float = 0f,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)
```

### 3. 圖文混合內容格式
```json
[
  {"type": "text", "content": "這是文字內容"},
  {"type": "image", "content": "uuid-filename.jpg"},
  {"type": "text", "content": "更多文字"}
]
```

## 🎨 UI組件詳解

### 1. RichTextEditText（核心組件）

**位置**：`app/src/main/java/com/erroranalysis/app/ui/widgets/RichTextEditText.kt`

**功能**：
- ✅ 圖文混合編輯和顯示
- ✅ 圖片插入和管理
- ✅ 雙指縮放圖片
- ✅ 雙擊切換圖片大小
- ✅ 只讀模式支援

**關鍵方法**：
```kotlin
// 設置圖文混合內容
fun setRichContent(jsonContent: String)

// 獲取圖文混合內容
fun getRichContent(): String

// 插入圖片
fun insertImage(bitmap: Bitmap): Boolean

// 設置只讀模式
fun setReadOnlyMode(readOnly: Boolean)
```

**圖片縮放實現**：
```kotlin
// 簡單雙指縮放（不依賴ScaleGestureDetector）
private fun handleSimpleScale(event: MotionEvent, actionMasked: Int) {
    // 計算雙指距離變化
    // 直接縮放圖片drawable.setBounds()
    // 調用invalidate()重繪
}
```

### 2. CardViewActivity（卡片檢視）

**位置**：`app/src/main/java/com/erroranalysis/app/ui/study/CardViewActivity.kt`

**功能**：
- ✅ 全版卡片檢視
- ✅ 點擊切換題目/答案
- ✅ 圖片縮放支援
- ✅ 快速編輯入口

**關鍵特色**：
```kotlin
// 點擊切換內容
private fun toggleContent() {
    showingAnswer = !showingAnswer
    if (showingAnswer) {
        binding.editContent.setRichContent(card.answer)
    } else {
        binding.editContent.setRichContent(card.question)
    }
    updateUI()
}
```

### 3. CardEditActivity（卡片編輯）

**位置**：`app/src/main/java/com/erroranalysis/app/ui/study/CardEditActivity.kt`

**功能**：
- ✅ 完整的卡片編輯功能
- ✅ 圖片插入（拍照/相冊）
- ✅ 標籤管理
- ✅ 難度設置

## 🖼️ 圖片處理系統

### 1. ImageStorageManager

**位置**：`app/src/main/java/com/erroranalysis/app/utils/ImageStorageManager.kt`

**功能**：
- ✅ 圖片保存到應用私有目錄
- ✅ 圖片載入和管理
- ✅ 自動文件名生成（UUID）
- ✅ 圖片壓縮和優化

**關鍵方法**：
```kotlin
// 保存圖片
fun saveImage(bitmap: Bitmap): String?

// 載入圖片
fun loadImage(filename: String): Bitmap?

// 刪除圖片
fun deleteImage(filename: String): Boolean
```

### 2. ZoomableImageSpan

**位置**：`RichTextEditText.kt` 內部類

**功能**：
- ✅ 可縮放的圖片Span
- ✅ 保存圖片文件名
- ✅ 支援drawable.setBounds()縮放

```kotlin
private class ZoomableImageSpan(
    drawable: Drawable,
    val imageFileName: String?
) : ImageSpan(drawable, ALIGN_BASELINE)
```

## 📱 用戶界面流程

### 1. 主要導航流程
```
MainActivity → StudyActivity → DeckDetailActivity → CardViewActivity
                            ↓
                         CardEditActivity
```

### 2. 卡片檢視流程
```
1. 點擊卡片 → CardViewActivity
2. 顯示題目
3. 點擊內容區域 → 切換到答案
4. 點擊編輯按鈕 → CardEditActivity
5. 圖片縮放：雙指縮放、雙擊切換
```

### 3. 卡片編輯流程
```
1. 長按卡片 → 選擇編輯 → CardEditActivity
2. 編輯題目/答案（支援圖文混合）
3. 添加圖片：拍照或從相冊選擇
4. 設置標籤和難度
5. 保存 → 返回卡組詳情
```

## 🔧 技術實現細節

### 1. 圖片縮放技術

**問題**：ScaleGestureDetector在某些設備上不穩定

**解決方案**：自實現簡單雙指縮放
```kotlin
// 計算兩指距離
private fun getDistance(event: MotionEvent): Float {
    val x = event.getX(0) - event.getX(1)
    val y = event.getY(0) - event.getY(1)
    return sqrt(x * x + y * y)
}

// 縮放邏輯
val scaleFactor = currentDistance / lastDistance
val newScale = currentScale * scaleFactor
val clampedScale = max(minScale, min(newScale, maxScale))
```

### 2. 圖文混合存儲

**格式**：JSON數組，每個元素包含type和content
```json
[
  {"type": "text", "content": "題目文字"},
  {"type": "image", "content": "abc123-image.jpg"},
  {"type": "text", "content": "更多文字"}
]
```

**優勢**：
- 順序保持
- 易於解析
- 支援任意圖文混合

### 3. 只讀模式實現

**CardViewActivity中的設置**：
```kotlin
binding.editContent.setReadOnlyMode(true)
```

**RichTextEditText中的實現**：
```kotlin
fun setReadOnlyMode(readOnly: Boolean) {
    if (readOnly) {
        isFocusable = true
        isFocusableInTouchMode = false
        isClickable = true
        isCursorVisible = false
        keyListener = null // 禁用鍵盤輸入
    }
}
```

## 🎯 關鍵功能狀態

### ✅ 已完成功能
- [x] 卡組創建、編輯、刪除
- [x] 卡片創建、編輯、刪除
- [x] 圖文混合編輯器
- [x] 圖片插入（拍照/相冊）
- [x] 卡片檢視界面
- [x] 點擊切換題目/答案
- [x] 圖片雙指縮放
- [x] 圖片雙擊切換大小
- [x] 卡片列表純文字預覽
- [x] 只讀模式支援

### 🔧 技術特色
- [x] 自定義RichTextEditText組件
- [x] 簡單可靠的雙指縮放實現
- [x] JSON格式的圖文混合存儲
- [x] 應用私有目錄圖片管理
- [x] Room數據庫持久化

### 📱 用戶體驗
- [x] 流暢的圖片縮放
- [x] 直觀的點擊切換
- [x] 友好的卡片預覽
- [x] 完整的編輯功能

## 🚨 已知問題和解決方案

### 1. 圖片縮放問題
**問題**：ScaleGestureDetector在某些設備不工作
**解決**：實現簡單的雙指距離計算縮放 ✅

### 2. 卡片列表顯示問題
**問題**：顯示JSON格式而非友好文字
**解決**：實現getPlainTextPreview()方法 ✅

### 3. 檢視模式衝突問題
**問題**：圖片縮放與點擊切換衝突
**解決**：創建專用CardViewActivity ✅

## 📞 重要提醒

### 1. 核心組件
**RichTextEditText** 是整個應用的核心，包含：
- 圖文混合編輯
- 圖片縮放功能
- 只讀模式支援

### 2. 關鍵實現
**簡單雙指縮放** 是目前最穩定的解決方案：
- 不依賴ScaleGestureDetector
- 直接計算雙指距離變化
- 手動調用drawable.setBounds()和invalidate()

### 3. 數據格式
**JSON格式的圖文混合內容** 是數據存儲的核心：
- 保持圖文順序
- 易於解析和顯示
- 支援任意複雜的圖文混合

### 4. 用戶體驗
**CardViewActivity** 提供最佳的卡片檢視體驗：
- 全版顯示
- 點擊切換
- 圖片縮放
- 快速編輯
