# CSV檔案編碼指南

## 🔍 問題說明

CSV檔案如果不是UTF-8編碼，中文字符會顯示為亂碼，導致：
- 欄位名稱無法正確識別
- 題目內容顯示亂碼
- 匯入失敗或結果錯誤

## 📝 正確的CSV創建方法

### 方法1：使用記事本（推薦）

1. **開啟記事本**
2. **輸入CSV內容**：
   ```
   題目ID,題目文字,題目圖片,答案
   Q001,有文字的題目,test1.jpg,答案1
   Q002,,test2.jpg,答案2
   Q003,純文字題目,,答案3
   ```
3. **另存新檔**
4. **檔案名稱**：`test.csv`
5. **編碼**：選擇「UTF-8」
6. **儲存**

### 方法2：使用Excel

1. **開啟Excel**
2. **輸入資料**（每個欄位一列）
3. **檔案 → 另存新檔**
4. **檔案類型**：選擇「CSV UTF-8 (逗號分隔) (*.csv)」
5. **儲存**

### 方法3：使用VS Code

1. **開啟VS Code**
2. **新增檔案**
3. **輸入CSV內容**
4. **右下角確認顯示「UTF-8」**
5. **儲存為 .csv 檔案**

### 方法4：使用提供的檔案

直接使用專案中提供的UTF-8檔案：
- `docs/test_direct.csv` - 簡單測試檔案
- `docs/test_image_import.csv` - 完整測試檔案

## ❌ 常見錯誤

### 錯誤1：使用ANSI編碼
**症狀**：中文顯示為 `???` 或亂碼
**解決**：重新保存為UTF-8編碼

### 錯誤2：Excel預設CSV
**症狀**：中文顯示為亂碼
**解決**：使用「CSV UTF-8」格式而不是普通「CSV」

### 錯誤3：複製貼上問題
**症狀**：從網頁或其他地方複製的內容有編碼問題
**解決**：手動重新輸入或確認編碼

## 🔧 編碼檢查方法

### 檢查1：用記事本開啟
1. 右鍵點擊CSV檔案
2. 選擇「開啟方式 → 記事本」
3. 檢查中文是否正常顯示
4. 如果顯示亂碼，重新保存為UTF-8

### 檢查2：用VS Code開啟
1. 用VS Code開啟CSV檔案
2. 檢查右下角編碼顯示
3. 如果不是UTF-8，點擊編碼 → 選擇「透過編碼重新開啟」→ UTF-8

### 檢查3：檔案屬性
1. 右鍵點擊檔案 → 內容
2. 檢查檔案大小（UTF-8中文檔案通常比ANSI大）

## 📋 測試檔案內容

### 簡單測試檔案
```csv
題目ID,題目文字,題目圖片,答案
Q001,有文字的題目,test1.jpg,答案1
Q002,,test2.jpg,答案2
Q003,純文字題目,,答案3
```

### 完整測試檔案
```csv
題目ID,題目文字,題目圖片,答案,標籤,難度
Q001,解這個方程式,math_eq1.jpg,x = 5,代數,簡單
Q002,,math_eq2.png,y = 3,代數,普通
Q003,分析這個圖表,diagram.jpg,見圖解,統計,困難
Q004,這是純文字題目,,42,基礎,簡單
Q005,,missing_image.jpg,找不到圖片,測試,普通
```

## 🎯 驗證方法

### 步驟1：檢查檔案編碼
- 用記事本開啟，中文正常顯示
- VS Code右下角顯示UTF-8

### 步驟2：檢查匯入日誌
```bash
adb logcat | grep "CSV標題"
```

應該看到：
```
CSV標題: [題目ID, 題目文字, 題目圖片, 答案]
```

而不是：
```
CSV標題: [??????, ??????, ??????, ????]
```

### 步驟3：檢查欄位映射
```bash
adb logcat | grep "欄位映射"
```

應該看到：
```
最終欄位映射: {id=0, questionText=1, questionImage=2, answer=3}
```

## 🚨 故障排除

### 問題：匯入後中文亂碼
**原因**：CSV檔案不是UTF-8編碼
**解決**：
1. 重新保存CSV為UTF-8編碼
2. 重新匯入

### 問題：欄位映射失敗
**症狀**：`questionImage` 沒有出現在映射中
**原因**：標題行亂碼導致無法識別
**解決**：確認CSV檔案UTF-8編碼正確

### 問題：題目內容亂碼
**原因**：資料行編碼問題
**解決**：重新創建UTF-8編碼的CSV檔案

## 💡 最佳實踐

1. **始終使用UTF-8編碼**
2. **使用提供的範例檔案作為模板**
3. **測試前先用記事本檢查編碼**
4. **避免從Excel直接保存為普通CSV**
5. **保留UTF-8編碼的備份檔案**

## 📞 技術支援

如果編碼問題仍然存在：

1. **提供檔案**：將有問題的CSV檔案提供給開發者
2. **提供日誌**：包含欄位映射和標題解析的日誌
3. **說明創建方法**：告知如何創建的CSV檔案
4. **系統信息**：作業系統和使用的編輯器

正確的UTF-8編碼是CSV匯入成功的關鍵！
