-- Merging decision tree log ---
manifest
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:2:1-120:12
INJECTED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:2:1-120:12
INJECTED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:2:1-120:12
INJECTED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:2:1-120:12
MERGED from [androidx.databinding:viewbinding:8.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85101096d2de545524fb7bf9c4900e97\transformed\viewbinding-8.7.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38790647496f2de6b4f9690090db3cca\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b2ee4f86ea3e068d2cf7eabfa30b7ed\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d092643f580c1526aee1a94274652a39\transformed\camera-video-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4261460e0bd453268643fb4c5e9e5210\transformed\camera-core-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cec44eb9b533eb3c3e9138490f323086\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7efb7f64467f693255020c5f2fa6ec22\transformed\camera-camera2-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e3a959bd7e2efbd30109f076a48f358\transformed\camera-view-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31d1c81eae710b5ee931222f1368004a\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e25ec97e005582fa7c282bb4a9e4a2\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a916e8a91b6ffc8f36aaec2a0a1c491\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.mlkit:text-recognition:16.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:text-recognition-chinese:16.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c2150adaa76af5db31b30b46c04134\transformed\play-services-mlkit-text-recognition-19.0.0\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.mlkit:text-recognition-bundled-common:16.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cd1edcab5507cc2cd129bdacf9bff40\transformed\text-recognition-bundled-common-16.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-chinese:16.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\227440721ba7735a0ddd8b949146c9ab\transformed\play-services-mlkit-text-recognition-chinese-16.0.0\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaca9f41a13f2d75f10de673be987a53\transformed\vision-common-17.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cc10637bfd0587c1c332e1f64a0d224\transformed\play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3e46e7c01988a1cfddab1c847c00ff0\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e85d97dc193bd3738aa89a331d62bca\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d9ffc143cbf5d78f49ada17a4468f7\transformed\play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b2ffb4071b55d56ea142c2666cd2dc\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3600a95969388f0fbd89436c623464d\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.ai.client.generativeai:generativeai:0.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5440166aa13484956a970db3ac526175\transformed\generativeai-0.2.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e1a9c52d1694669feb25eecf3cc5b2\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d64f950101c4975406aad72599e3647\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e835174e17914dd0b06ad99423a15442\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29049e9d10c499dc48e281da00c0c59d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b7926a336e8f3b7191e0c8dab684b13\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79d455112cea3479db2149b68382c40d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1d2c7060541093d22219c6012379f31\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c93e8c507a66ecbf82a3efa9080d75e0\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e346cfe2d3dcd362825208313698648a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c1f8acf66f587cb6b45a6337b2cd536\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70c89a09241c3334a7dcc790586bec5d\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b75e659807ab03c75f96ee5b29080761\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\813f3cbef3e687843ee68068ffb5a850\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8535209a3cbfed1c06491377567aea9e\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a98eafdb08f6d50779fe4f3ae8efe79\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6eb98dae86a5135dbce89c2b3359d8ad\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57bb1e110bac3f893ab69e80b3d9d714\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f55bbb0fc1a607d9391120d30b0a7f85\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\900e86d33ccadc39887326a596d53716\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3519121a88f69b09032f09cac307307a\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca20d7f7ec770db9b211750429383501\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6bb91a964a74061bff42008fdc20880\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc38da6ca6458e654156fe042ad2791f\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b92e5da12ca3853036c54440ee50caf0\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb968a6e242fba186acf648e7feae171\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47a19ccc4b29397ceb50e6fd12130048\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cc42f0d7261c1b19e16a2cc62808752\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [:opencv] C:\augment-projects\HomeWork\android\ErrorAnalysisApp\sdk\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53c8fab722370eb020edd167d01d0ea8\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f6d1b1b00b1cbfa0c3fca9c3847de5f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f3d3836f0e2cbbc09db653ca897412a\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61ef43466e0cfb065c138fe340d15e7c\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83ddf1ee92c4d2c3d13a20c16d08455a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4522ac64cf9655a4c21b0928d5bc6d0c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bf951999c6735880b0c5abae12b18b9\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\937a247e22710787351e2c90b805c633\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\545e41559c037d9241e6669318a4280b\transformed\exifinterface-1.3.2\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e389dadae58d45615f4e8cb32bf706d\transformed\transport-api-2.2.1\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\58c7c53ebddce8bf981ae7af6525a4e1\transformed\firebase-components-16.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\299f5414e2a14c2248e0edd57a0589aa\transformed\firebase-encoders-json-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a82dfe9655caea986fb1e47a1226c597\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6033395c3e5acce361fa7495cf348b94\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64f7b58f4aa97bad131d27fa0750e8f0\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f05244648490f155dc155df99016c713\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.CAMERA
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:5:5-65
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:5:22-62
uses-feature#android.hardware.camera
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:6:5-84
	android:required
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:6:58-81
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:6:19-57
uses-feature#android.hardware.camera.autofocus
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:7:5-95
	android:required
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:7:68-92
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:7:19-67
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:10:5-11:38
	android:maxSdkVersion
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:11:9-35
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:10:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:12:5-80
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:12:22-77
uses-permission#android.permission.INTERNET
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:15:5-67
MERGED from [com.google.ai.client.generativeai:generativeai:0.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5440166aa13484956a970db3ac526175\transformed\generativeai-0.2.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.ai.client.generativeai:generativeai:0.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5440166aa13484956a970db3ac526175\transformed\generativeai-0.2.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:15:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:16:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:16:22-76
application
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:18:5-118:19
INJECTED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:18:5-118:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38790647496f2de6b4f9690090db3cca\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38790647496f2de6b4f9690090db3cca\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b2ee4f86ea3e068d2cf7eabfa30b7ed\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b2ee4f86ea3e068d2cf7eabfa30b7ed\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4261460e0bd453268643fb4c5e9e5210\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4261460e0bd453268643fb4c5e9e5210\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7efb7f64467f693255020c5f2fa6ec22\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7efb7f64467f693255020c5f2fa6ec22\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaca9f41a13f2d75f10de673be987a53\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaca9f41a13f2d75f10de673be987a53\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cc10637bfd0587c1c332e1f64a0d224\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cc10637bfd0587c1c332e1f64a0d224\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e85d97dc193bd3738aa89a331d62bca\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e85d97dc193bd3738aa89a331d62bca\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d9ffc143cbf5d78f49ada17a4468f7\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d9ffc143cbf5d78f49ada17a4468f7\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57bb1e110bac3f893ab69e80b3d9d714\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57bb1e110bac3f893ab69e80b3d9d714\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83ddf1ee92c4d2c3d13a20c16d08455a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83ddf1ee92c4d2c3d13a20c16d08455a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4522ac64cf9655a4c21b0928d5bc6d0c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4522ac64cf9655a4c21b0928d5bc6d0c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f05244648490f155dc155df99016c713\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f05244648490f155dc155df99016c713\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:24:9-35
	android:label
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:22:9-41
	android:roundIcon
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:23:9-50
	android:icon
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:21:9-45
	android:allowBackup
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:20:9-35
	android:theme
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:25:9-54
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:19:9-49
activity#com.erroranalysis.app.ui.main.SimpleMainActivity
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:28:9-36:20
	android:exported
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:30:13-36
	android:theme
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:31:13-58
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:29:13-55
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:32:13-35:29
action#android.intent.action.MAIN
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:33:17-69
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:33:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:34:17-77
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:34:27-74
activity#com.erroranalysis.app.ui.camera.CameraActivity
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:39:9-42:52
	android:screenOrientation
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:42:13-49
	android:exported
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:41:13-37
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:40:13-53
activity#com.erroranalysis.app.ui.camera.SimpleCameraActivity
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:45:9-48:52
	android:screenOrientation
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:48:13-49
	android:exported
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:47:13-37
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:46:13-59
activity#com.erroranalysis.app.ui.camera.PhotoEditActivity
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:51:9-54:52
	android:screenOrientation
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:54:13-49
	android:exported
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:53:13-37
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:52:13-56
activity#com.erroranalysis.app.ui.camera.CropOverlayTestActivity
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:57:9-60:52
	android:screenOrientation
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:60:13-49
	android:exported
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:59:13-37
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:58:13-62
activity#com.erroranalysis.app.ui.selection.QuestionSelectionActivity
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:63:9-66:52
	android:screenOrientation
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:66:13-49
	android:exported
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:65:13-37
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:64:13-67
activity#com.erroranalysis.app.ui.analysis.AnalysisActivity
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:69:9-71:40
	android:exported
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:71:13-37
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:70:13-57
activity#com.erroranalysis.app.ui.study.SimpleStudyActivity
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:76:9-80:61
	android:exported
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:78:13-37
	android:configChanges
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:79:13-72
	android:theme
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:80:13-58
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:77:13-57
activity#com.erroranalysis.app.ui.study.DeckDetailActivity
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:83:9-87:61
	android:parentActivityName
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:86:13-71
	android:exported
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:85:13-37
	android:theme
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:87:13-58
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:84:13-56
activity#com.erroranalysis.app.ui.study.CardEditActivity
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:90:9-95:58
	android:parentActivityName
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:93:13-70
	android:windowSoftInputMode
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:95:13-55
	android:exported
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:92:13-37
	android:theme
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:94:13-58
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:91:13-54
activity#com.erroranalysis.app.ui.study.CardViewerActivity
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:98:9-102:61
	android:parentActivityName
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:101:13-70
	android:exported
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:100:13-37
	android:theme
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:102:13-58
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:99:13-56
activity#com.erroranalysis.app.ui.study.BatchImportActivity
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:105:9-109:61
	android:parentActivityName
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:108:13-71
	android:exported
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:107:13-37
	android:theme
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:109:13-58
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:106:13-57
activity#com.erroranalysis.app.ui.settings.SettingsActivity
ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:112:9-116:61
	android:parentActivityName
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:115:13-71
	android:exported
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:114:13-37
	android:theme
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:116:13-58
	android:name
		ADDED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml:113:13-57
uses-sdk
INJECTED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml
INJECTED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85101096d2de545524fb7bf9c4900e97\transformed\viewbinding-8.7.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85101096d2de545524fb7bf9c4900e97\transformed\viewbinding-8.7.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38790647496f2de6b4f9690090db3cca\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38790647496f2de6b4f9690090db3cca\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b2ee4f86ea3e068d2cf7eabfa30b7ed\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b2ee4f86ea3e068d2cf7eabfa30b7ed\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d092643f580c1526aee1a94274652a39\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d092643f580c1526aee1a94274652a39\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4261460e0bd453268643fb4c5e9e5210\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4261460e0bd453268643fb4c5e9e5210\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cec44eb9b533eb3c3e9138490f323086\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cec44eb9b533eb3c3e9138490f323086\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7efb7f64467f693255020c5f2fa6ec22\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7efb7f64467f693255020c5f2fa6ec22\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e3a959bd7e2efbd30109f076a48f358\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e3a959bd7e2efbd30109f076a48f358\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31d1c81eae710b5ee931222f1368004a\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31d1c81eae710b5ee931222f1368004a\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e25ec97e005582fa7c282bb4a9e4a2\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3e25ec97e005582fa7c282bb4a9e4a2\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a916e8a91b6ffc8f36aaec2a0a1c491\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a916e8a91b6ffc8f36aaec2a0a1c491\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.mlkit:text-recognition:16.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:text-recognition:16.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74259ffef1ef72b18d792573116751ba\transformed\text-recognition-16.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:text-recognition-chinese:16.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:text-recognition-chinese:16.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7839ff645dc188a379b714d44e0100de\transformed\text-recognition-chinese-16.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c2150adaa76af5db31b30b46c04134\transformed\play-services-mlkit-text-recognition-19.0.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97c2150adaa76af5db31b30b46c04134\transformed\play-services-mlkit-text-recognition-19.0.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:text-recognition-bundled-common:16.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cd1edcab5507cc2cd129bdacf9bff40\transformed\text-recognition-bundled-common-16.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:text-recognition-bundled-common:16.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cd1edcab5507cc2cd129bdacf9bff40\transformed\text-recognition-bundled-common-16.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-chinese:16.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\227440721ba7735a0ddd8b949146c9ab\transformed\play-services-mlkit-text-recognition-chinese-16.0.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-chinese:16.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\227440721ba7735a0ddd8b949146c9ab\transformed\play-services-mlkit-text-recognition-chinese-16.0.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaca9f41a13f2d75f10de673be987a53\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaca9f41a13f2d75f10de673be987a53\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cc10637bfd0587c1c332e1f64a0d224\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cc10637bfd0587c1c332e1f64a0d224\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3e46e7c01988a1cfddab1c847c00ff0\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3e46e7c01988a1cfddab1c847c00ff0\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e85d97dc193bd3738aa89a331d62bca\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e85d97dc193bd3738aa89a331d62bca\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d9ffc143cbf5d78f49ada17a4468f7\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d9ffc143cbf5d78f49ada17a4468f7\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b2ffb4071b55d56ea142c2666cd2dc\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b2ffb4071b55d56ea142c2666cd2dc\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3600a95969388f0fbd89436c623464d\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3600a95969388f0fbd89436c623464d\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.ai.client.generativeai:generativeai:0.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5440166aa13484956a970db3ac526175\transformed\generativeai-0.2.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.ai.client.generativeai:generativeai:0.2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5440166aa13484956a970db3ac526175\transformed\generativeai-0.2.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e1a9c52d1694669feb25eecf3cc5b2\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e1a9c52d1694669feb25eecf3cc5b2\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d64f950101c4975406aad72599e3647\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d64f950101c4975406aad72599e3647\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e835174e17914dd0b06ad99423a15442\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e835174e17914dd0b06ad99423a15442\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29049e9d10c499dc48e281da00c0c59d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29049e9d10c499dc48e281da00c0c59d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b7926a336e8f3b7191e0c8dab684b13\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b7926a336e8f3b7191e0c8dab684b13\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79d455112cea3479db2149b68382c40d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79d455112cea3479db2149b68382c40d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1d2c7060541093d22219c6012379f31\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1d2c7060541093d22219c6012379f31\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c93e8c507a66ecbf82a3efa9080d75e0\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c93e8c507a66ecbf82a3efa9080d75e0\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e346cfe2d3dcd362825208313698648a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e346cfe2d3dcd362825208313698648a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c1f8acf66f587cb6b45a6337b2cd536\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c1f8acf66f587cb6b45a6337b2cd536\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70c89a09241c3334a7dcc790586bec5d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70c89a09241c3334a7dcc790586bec5d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b75e659807ab03c75f96ee5b29080761\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b75e659807ab03c75f96ee5b29080761\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\813f3cbef3e687843ee68068ffb5a850\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\813f3cbef3e687843ee68068ffb5a850\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8535209a3cbfed1c06491377567aea9e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8535209a3cbfed1c06491377567aea9e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a98eafdb08f6d50779fe4f3ae8efe79\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a98eafdb08f6d50779fe4f3ae8efe79\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6eb98dae86a5135dbce89c2b3359d8ad\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6eb98dae86a5135dbce89c2b3359d8ad\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57bb1e110bac3f893ab69e80b3d9d714\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57bb1e110bac3f893ab69e80b3d9d714\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f55bbb0fc1a607d9391120d30b0a7f85\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f55bbb0fc1a607d9391120d30b0a7f85\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\900e86d33ccadc39887326a596d53716\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\900e86d33ccadc39887326a596d53716\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3519121a88f69b09032f09cac307307a\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3519121a88f69b09032f09cac307307a\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca20d7f7ec770db9b211750429383501\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca20d7f7ec770db9b211750429383501\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6bb91a964a74061bff42008fdc20880\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6bb91a964a74061bff42008fdc20880\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc38da6ca6458e654156fe042ad2791f\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc38da6ca6458e654156fe042ad2791f\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b92e5da12ca3853036c54440ee50caf0\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b92e5da12ca3853036c54440ee50caf0\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb968a6e242fba186acf648e7feae171\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb968a6e242fba186acf648e7feae171\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47a19ccc4b29397ceb50e6fd12130048\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47a19ccc4b29397ceb50e6fd12130048\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cc42f0d7261c1b19e16a2cc62808752\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cc42f0d7261c1b19e16a2cc62808752\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [:opencv] C:\augment-projects\HomeWork\android\ErrorAnalysisApp\sdk\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:opencv] C:\augment-projects\HomeWork\android\ErrorAnalysisApp\sdk\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53c8fab722370eb020edd167d01d0ea8\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53c8fab722370eb020edd167d01d0ea8\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f6d1b1b00b1cbfa0c3fca9c3847de5f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f6d1b1b00b1cbfa0c3fca9c3847de5f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f3d3836f0e2cbbc09db653ca897412a\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f3d3836f0e2cbbc09db653ca897412a\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61ef43466e0cfb065c138fe340d15e7c\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61ef43466e0cfb065c138fe340d15e7c\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83ddf1ee92c4d2c3d13a20c16d08455a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83ddf1ee92c4d2c3d13a20c16d08455a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4522ac64cf9655a4c21b0928d5bc6d0c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4522ac64cf9655a4c21b0928d5bc6d0c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bf951999c6735880b0c5abae12b18b9\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bf951999c6735880b0c5abae12b18b9\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\937a247e22710787351e2c90b805c633\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\937a247e22710787351e2c90b805c633\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\545e41559c037d9241e6669318a4280b\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\545e41559c037d9241e6669318a4280b\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e389dadae58d45615f4e8cb32bf706d\transformed\transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e389dadae58d45615f4e8cb32bf706d\transformed\transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\58c7c53ebddce8bf981ae7af6525a4e1\transformed\firebase-components-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\58c7c53ebddce8bf981ae7af6525a4e1\transformed\firebase-components-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\299f5414e2a14c2248e0edd57a0589aa\transformed\firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\299f5414e2a14c2248e0edd57a0589aa\transformed\firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a82dfe9655caea986fb1e47a1226c597\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a82dfe9655caea986fb1e47a1226c597\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6033395c3e5acce361fa7495cf348b94\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6033395c3e5acce361fa7495cf348b94\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64f7b58f4aa97bad131d27fa0750e8f0\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64f7b58f4aa97bad131d27fa0750e8f0\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f05244648490f155dc155df99016c713\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f05244648490f155dc155df99016c713\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\augment-projects\HomeWork\android\ErrorAnalysisApp\app\src\main\AndroidManifest.xml
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4261460e0bd453268643fb4c5e9e5210\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7efb7f64467f693255020c5f2fa6ec22\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7efb7f64467f693255020c5f2fa6ec22\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
	android:enabled
		ADDED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4261460e0bd453268643fb4c5e9e5210\transformed\camera-core-1.3.1\AndroidManifest.xml:31:13-36
	android:exported
		ADDED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4261460e0bd453268643fb4c5e9e5210\transformed\camera-core-1.3.1\AndroidManifest.xml:32:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4261460e0bd453268643fb4c5e9e5210\transformed\camera-core-1.3.1\AndroidManifest.xml:33:13-75
	android:name
		ADDED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4261460e0bd453268643fb4c5e9e5210\transformed\camera-core-1.3.1\AndroidManifest.xml:30:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7efb7f64467f693255020c5f2fa6ec22\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7efb7f64467f693255020c5f2fa6ec22\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7efb7f64467f693255020c5f2fa6ec22\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaca9f41a13f2d75f10de673be987a53\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaca9f41a13f2d75f10de673be987a53\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-text-recognition-common:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f2cbdd5273cc0d97a8b05c0709b75d7\transformed\play-services-mlkit-text-recognition-common-19.0.0\AndroidManifest.xml:13:17-114
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaca9f41a13f2d75f10de673be987a53\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaca9f41a13f2d75f10de673be987a53\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaca9f41a13f2d75f10de673be987a53\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17b1a3ed59883925b112eaf6e6e68028\transformed\common-18.8.0\AndroidManifest.xml:21:17-120
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cc10637bfd0587c1c332e1f64a0d224\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cc10637bfd0587c1c332e1f64a0d224\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cc10637bfd0587c1c332e1f64a0d224\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cc10637bfd0587c1c332e1f64a0d224\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d9ffc143cbf5d78f49ada17a4468f7\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d9ffc143cbf5d78f49ada17a4468f7\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d9ffc143cbf5d78f49ada17a4468f7\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57bb1e110bac3f893ab69e80b3d9d714\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57bb1e110bac3f893ab69e80b3d9d714\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4522ac64cf9655a4c21b0928d5bc6d0c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4522ac64cf9655a4c21b0928d5bc6d0c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\566608dc17a5bc9771ee92b19227f0bc\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.erroranalysis.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.erroranalysis.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2087ae0d0678f91d06664b94633b22db\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57bb1e110bac3f893ab69e80b3d9d714\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57bb1e110bac3f893ab69e80b3d9d714\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57bb1e110bac3f893ab69e80b3d9d714\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ca6843149c2edf39fd943d4ce5136f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad1da2784d22a939525b383baf3a3edf\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6c4ec8d3480b560a14d350ae54fc3b0\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
