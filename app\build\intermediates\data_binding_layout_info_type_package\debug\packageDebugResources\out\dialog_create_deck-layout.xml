<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_create_deck" modulePackage="com.erroranalysis.app" filePath="app\src\main\res\layout\dialog_create_deck.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_create_deck_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="246" endOffset="14"/></Target><Target id="@+id/tab_layout" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="16" startOffset="4" endLine="21" endOffset="29"/></Target><Target id="@+id/layout_custom" view="ScrollView"><Expressions/><location startLine="23" startOffset="4" endLine="207" endOffset="16"/></Target><Target id="@+id/card_preview" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="41" startOffset="12" endLine="88" endOffset="63"/></Target><Target id="@+id/text_preview_icon" view="TextView"><Expressions/><location startLine="57" startOffset="20" endLine="62" endOffset="49"/></Target><Target id="@+id/text_preview_name" view="TextView"><Expressions/><location startLine="64" startOffset="20" endLine="75" endOffset="50"/></Target><Target id="@+id/text_preview_count" view="TextView"><Expressions/><location startLine="77" startOffset="20" endLine="84" endOffset="49"/></Target><Target id="@+id/edit_deck_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="96" startOffset="16" endLine="101" endOffset="42"/></Target><Target id="@+id/recycler_subject_templates" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="113" startOffset="12" endLine="117" endOffset="52"/></Target><Target id="@+id/recycler_style_templates" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="127" startOffset="12" endLine="131" endOffset="52"/></Target><Target id="@+id/recycler_exam_templates" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="141" startOffset="12" endLine="145" endOffset="52"/></Target><Target id="@+id/recycler_colors" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="155" startOffset="12" endLine="159" endOffset="52"/></Target><Target id="@+id/edit_subject" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="167" startOffset="16" endLine="172" endOffset="42"/></Target><Target id="@+id/edit_tags" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="182" startOffset="16" endLine="186" endOffset="46"/></Target><Target id="@+id/edit_description" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="196" startOffset="16" endLine="201" endOffset="42"/></Target><Target id="@+id/layout_quick" view="LinearLayout"><Expressions/><location startLine="209" startOffset="4" endLine="221" endOffset="18"/></Target><Target id="@+id/recycler_quick_templates" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="216" startOffset="8" endLine="219" endOffset="50"/></Target><Target id="@+id/button_cancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="230" startOffset="8" endLine="236" endOffset="35"/></Target><Target id="@+id/button_create" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="238" startOffset="8" endLine="242" endOffset="35"/></Target></Targets></Layout>