.com.erroranalysis.app.ErrorAnalysisApplication8com.erroranalysis.app.ErrorAnalysisApplication.Companion*com.erroranalysis.app.data.DeckDataManager4com.erroranalysis.app.data.DeckDataManager.Companion6com.erroranalysis.app.data.DeckDataManager.ContentItem,com.erroranalysis.app.ui.base.ThemedActivity.com.erroranalysis.app.ui.camera.CameraActivity8com.erroranalysis.app.ui.camera.CameraActivity.Companion/com.erroranalysis.app.ui.camera.CameraViewModel<com.erroranalysis.app.ui.camera.CameraViewModel.CaptureState7com.erroranalysis.app.ui.camera.DocumentDetectionResult6com.erroranalysis.app.ui.camera.DocumentDetectionState5com.erroranalysis.app.ui.camera.CombinedImageAnalyzer?com.erroranalysis.app.ui.camera.CombinedImageAnalyzer.Companion7com.erroranalysis.app.ui.camera.CropOverlayTestActivity4com.erroranalysis.app.ui.camera.CropSelectionOverlay=com.erroranalysis.app.ui.camera.CropSelectionOverlay.DragMode7com.erroranalysis.app.ui.camera.DocumentBoundaryOverlayAcom.erroranalysis.app.ui.camera.DocumentBoundaryOverlay.Companion9com.erroranalysis.app.ui.camera.DocumentDetectionAnalyzerCcom.erroranalysis.app.ui.camera.DocumentDetectionAnalyzer.Companion-com.erroranalysis.app.ui.camera.FocusAnalyzer*com.erroranalysis.app.ui.camera.FocusState1com.erroranalysis.app.ui.camera.PhotoEditActivity;com.erroranalysis.app.ui.camera.PhotoEditActivity.Companion4com.erroranalysis.app.ui.camera.SimpleCameraActivity>com.erroranalysis.app.ui.camera.SimpleCameraActivity.Companion0com.erroranalysis.app.ui.main.SimpleMainActivity2com.erroranalysis.app.ui.settings.SettingsActivity7com.erroranalysis.app.ui.settings.adapters.ThemeAdapterGcom.erroranalysis.app.ui.settings.adapters.ThemeAdapter.ThemeViewHolder2com.erroranalysis.app.ui.study.BatchImportActivity/com.erroranalysis.app.ui.study.CardEditActivity9com.erroranalysis.app.ui.study.CardEditActivity.Companion)com.erroranalysis.app.ui.study.CardFilter+com.erroranalysis.app.ui.study.FilterResult1com.erroranalysis.app.ui.study.CardViewerActivity;com.erroranalysis.app.ui.study.CardViewerActivity.Companion1com.erroranalysis.app.ui.study.DeckDetailActivity;com.erroranalysis.app.ui.study.DeckDetailActivity.Companion2com.erroranalysis.app.ui.study.SimpleStudyActivity<com.erroranalysis.app.ui.study.SimpleStudyActivity.Companion)com.erroranalysis.app.ui.study.SimpleDeck(com.erroranalysis.app.ui.study.StudyCard*com.erroranalysis.app.ui.study.CardMastery-com.erroranalysis.app.ui.study.CardDifficulty+com.erroranalysis.app.ui.study.ReviewResult4com.erroranalysis.app.ui.study.adapters.ColorAdapterDcom.erroranalysis.app.ui.study.adapters.ColorAdapter.ColorViewHolderFcom.erroranalysis.app.ui.study.adapters.ColorAdapter.ColorDiffCallback3com.erroranalysis.app.ui.study.adapters.IconAdapterBcom.erroranalysis.app.ui.study.adapters.IconAdapter.IconViewHolder0com.erroranalysis.app.ui.study.adapters.IconItem6com.erroranalysis.app.ui.study.adapters.IconCollection9com.erroranalysis.app.ui.study.adapters.SimpleDeckAdapterHcom.erroranalysis.app.ui.study.adapters.SimpleDeckAdapter.DeckViewHolderJcom.erroranalysis.app.ui.study.adapters.SimpleDeckAdapter.DeckDiffCallback8com.erroranalysis.app.ui.study.adapters.StudyCardAdapterGcom.erroranalysis.app.ui.study.adapters.StudyCardAdapter.CardViewHolderDcom.erroranalysis.app.ui.study.adapters.StudyCardAdapter.ContentItemIcom.erroranalysis.app.ui.study.adapters.StudyCardAdapter.CardDiffCallback<com.erroranalysis.app.ui.test.DocumentCorrectionTestActivityFcom.erroranalysis.app.ui.test.DocumentCorrectionTestActivity.Companion'com.erroranalysis.app.ui.theme.AppTheme.com.erroranalysis.app.ui.theme.ThemeCollection+com.erroranalysis.app.ui.theme.ThemeManager5com.erroranalysis.app.ui.theme.ThemeManager.Companion?com.erroranalysis.app.ui.theme.ThemeManager.ThemeChangeListener)com.erroranalysis.app.ui.theme.ThemeUtils1com.erroranalysis.app.ui.widgets.RichTextEditText;com.erroranalysis.app.ui.widgets.RichTextEditText.Companion;com.erroranalysis.app.ui.widgets.RichTextEditText.ImageInfoCcom.erroranalysis.app.ui.widgets.RichTextEditText.ZoomableImageSpan=com.erroranalysis.app.ui.widgets.RichTextEditText.ContentItem.com.erroranalysis.app.utils.BatchImportManager8com.erroranalysis.app.utils.BatchImportManager.Companion(com.erroranalysis.app.utils.ImportResult0com.erroranalysis.app.utils.ImportResult.Success.com.erroranalysis.app.utils.ImportResult.Error4com.erroranalysis.app.utils.DocumentBoundaryDetector>com.erroranalysis.app.utils.DocumentBoundaryDetector.Companion-com.erroranalysis.app.utils.DocumentProcessor7com.erroranalysis.app.utils.DocumentProcessor.Companion1com.erroranalysis.app.utils.DocumentProcessResult9com.erroranalysis.app.utils.DocumentProcessResult.Success7com.erroranalysis.app.utils.DocumentProcessResult.Error%com.erroranalysis.app.utils.ImageInfo+com.erroranalysis.app.utils.GeminiAIService5com.erroranalysis.app.utils.GeminiAIService.Companion5com.erroranalysis.app.utils.GridSpacingItemDecoration/com.erroranalysis.app.utils.ImageStorageManager9com.erroranalysis.app.utils.ImageStorageManager.Companion,com.erroranalysis.app.utils.MathFormatHelper%com.erroranalysis.app.utils.OCRHelper/com.erroranalysis.app.utils.OCRHelper.Companion-com.erroranalysis.app.utils.OpenCVInitializer)com.erroranalysis.app.utils.OpenCVManager0com.erroranalysis.app.utils.PerspectiveCorrector:com.erroranalysis.app.utils.PerspectiveCorrector.Companion2com.erroranalysis.app.databinding.ItemThemeBinding9com.erroranalysis.app.databinding.ActivityCardEditBinding6com.erroranalysis.app.databinding.ItemStudyCardBinding9com.erroranalysis.app.databinding.ActivitySettingsBinding2com.erroranalysis.app.databinding.ItemColorBinding;com.erroranalysis.app.databinding.ActivityDeckDetailBinding<com.erroranalysis.app.databinding.ActivityBatchImportBinding<com.erroranalysis.app.databinding.ActivitySimpleStudyBinding9com.erroranalysis.app.databinding.DialogCardFilterBinding7com.erroranalysis.app.databinding.ActivityCameraBinding9com.erroranalysis.app.databinding.ItemIconSelectorBinding7com.erroranalysis.app.databinding.ItemSimpleDeckBinding;com.erroranalysis.app.databinding.ActivityCardViewerBinding5com.erroranalysis.app.databinding.ActivityMainBinding:com.erroranalysis.app.databinding.ActivityPhotoEditBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          