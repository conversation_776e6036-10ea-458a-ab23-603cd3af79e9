package com.erroranalysis.app.ui.study.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.erroranalysis.app.databinding.ItemIconSelectorBinding

/**
 * 圖標選擇適配器
 */
class IconAdapter(
    private val icons: List<IconItem>,
    private val onIconSelected: (String) -> Unit
) : RecyclerView.Adapter<IconAdapter.IconViewHolder>() {

    private var selectedIcon: String = icons.firstOrNull()?.emoji ?: "🎯"

    fun setSelectedIcon(icon: String) {
        android.util.Log.d("IconAdapter", "設置選中圖標: $icon (之前: $selectedIcon)")
        val oldPosition = icons.indexOfFirst { it.emoji == selectedIcon }
        selectedIcon = icon
        val newPosition = icons.indexOfFirst { it.emoji == selectedIcon }

        android.util.Log.d("IconAdapter", "舊位置: $oldPosition, 新位置: $newPosition")
        if (oldPosition != -1) notifyItemChanged(oldPosition)
        if (newPosition != -1) notifyItemChanged(newPosition)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): IconViewHolder {
        android.util.Log.d("IconAdapter", "創建ViewHolder")
        val binding = ItemIconSelectorBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return IconViewHolder(binding)
    }

    override fun onBindViewHolder(holder: IconViewHolder, position: Int) {
        android.util.Log.d("IconAdapter", "綁定ViewHolder位置: $position, 圖標: ${icons[position].emoji}")
        holder.bind(icons[position])
    }

    override fun getItemCount() = icons.size

    inner class IconViewHolder(
        private val binding: ItemIconSelectorBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(iconItem: IconItem) {
            binding.textIcon.text = iconItem.emoji
            binding.textIconName.text = iconItem.name
            
            // 設置選中狀態 - 使用輕盈的邊框效果
            val isSelected = iconItem.emoji == selectedIcon
            if (isSelected) {
                binding.cardIcon.strokeWidth = 3
                binding.cardIcon.cardElevation = 4f
                binding.cardIcon.setCardBackgroundColor(
                    android.graphics.Color.parseColor("#E3F2FD") // 淡藍色背景
                )
            } else {
                binding.cardIcon.strokeWidth = 0
                binding.cardIcon.cardElevation = 0f
                binding.cardIcon.setCardBackgroundColor(
                    android.graphics.Color.TRANSPARENT
                )
            }

            // 設置點擊事件 - 同時設置到根佈局和卡片上
            val clickListener = {
                android.util.Log.d("IconAdapter", "點擊圖標: ${iconItem.emoji}")
                setSelectedIcon(iconItem.emoji)
                onIconSelected(iconItem.emoji)
            }

            binding.root.setOnClickListener { clickListener() }
            binding.cardIcon.setOnClickListener { clickListener() }
        }
    }
}

/**
 * 圖標項目數據類
 */
data class IconItem(
    val emoji: String,
    val name: String,
    val category: String = "general"
)

/**
 * 預定義圖標集合
 */
object IconCollection {
    
    val subjectIcons = listOf(
        IconItem("🧮", "數學"),
        IconItem("⚡", "物理"),
        IconItem("🧪", "化學"),
        IconItem("🌱", "生物"),
        IconItem("🌍", "英文"),
        IconItem("🖋️", "國文"),
        IconItem("🏛️", "歷史"),
        IconItem("🗺️", "地理"),
        IconItem("💻", "程式"),
        IconItem("🎯", "其他")
    )
    
    val generalIcons = listOf(
        IconItem("📚", "書本"),
        IconItem("📖", "課本"),
        IconItem("📝", "筆記"),
        IconItem("🎓", "學習"),
        IconItem("🏆", "成就"),
        IconItem("⭐", "重要"),
        IconItem("💡", "想法"),
        IconItem("🔥", "熱門"),
        IconItem("❤️", "喜愛"),
        IconItem("🚀", "進步")
    )
    
    val funIcons = listOf(
        IconItem("🎨", "創意"),
        IconItem("🎵", "音樂"),
        IconItem("🎮", "遊戲"),
        IconItem("🌟", "明星"),
        IconItem("🦄", "獨角獸"),
        IconItem("🌈", "彩虹"),
        IconItem("🎪", "馬戲團"),
        IconItem("🎭", "戲劇"),
        IconItem("🎯", "目標"),
        IconItem("🎲", "骰子")
    )
    
    fun getAllIcons(): List<IconItem> {
        return subjectIcons + generalIcons + funIcons
    }
    
    fun getIconsByCategory(category: String): List<IconItem> {
        return when (category) {
            "subject" -> subjectIcons
            "general" -> generalIcons
            "fun" -> funIcons
            else -> getAllIcons()
        }
    }
}
