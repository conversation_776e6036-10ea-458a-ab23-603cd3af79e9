#!/bin/bash

# ErrorAnalysisApp 高效編譯腳本
# 使用增量編譯，避免不必要的clean操作

echo "🚀 ErrorAnalysisApp 編譯腳本"
echo "================================"

# 檢查當前目錄
if [ ! -f "gradlew" ]; then
    echo "❌ 錯誤: 請在項目根目錄執行此腳本"
    exit 1
fi

# 顯示編譯選項
echo "選擇編譯模式:"
echo "1. 快速編譯 (增量編譯, ~30秒)"
echo "2. 完整編譯 (清理+重建, ~5分鐘)"
echo "3. 僅檢查語法"
echo "4. 安裝到設備"
echo "5. 查看APK信息"

read -p "請選擇 (1-5): " choice

case $choice in
    1)
        echo "🔄 執行快速增量編譯..."
        start_time=$(date +%s)
        ./gradlew assembleDebug
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        echo "✅ 編譯完成! 耗時: ${duration}秒"
        echo "📱 APK位置: app/build/outputs/apk/debug/app-debug.apk"
        ;;
    2)
        echo "🧹 執行完整清理編譯..."
        echo "⚠️  警告: 這將耗時約5分鐘"
        read -p "確定要繼續嗎? (y/N): " confirm
        if [[ $confirm == [yY] ]]; then
            start_time=$(date +%s)
            ./gradlew clean assembleDebug
            end_time=$(date +%s)
            duration=$((end_time - start_time))
            echo "✅ 完整編譯完成! 耗時: ${duration}秒"
            echo "📱 APK位置: app/build/outputs/apk/debug/app-debug.apk"
        else
            echo "❌ 取消編譯"
        fi
        ;;
    3)
        echo "🔍 檢查語法..."
        ./gradlew compileDebugKotlin
        echo "✅ 語法檢查完成"
        ;;
    4)
        echo "📱 安裝到設備..."
        if [ -f "app/build/outputs/apk/debug/app-debug.apk" ]; then
            adb install -r app/build/outputs/apk/debug/app-debug.apk
            if [ $? -eq 0 ]; then
                echo "✅ 安裝成功"
            else
                echo "❌ 安裝失敗，請檢查設備連接"
            fi
        else
            echo "❌ APK文件不存在，請先編譯"
        fi
        ;;
    5)
        echo "📊 APK信息:"
        if [ -f "app/build/outputs/apk/debug/app-debug.apk" ]; then
            ls -lh app/build/outputs/apk/debug/app-debug.apk
            echo ""
            echo "APK詳細信息:"
            aapt dump badging app/build/outputs/apk/debug/app-debug.apk | head -5
        else
            echo "❌ APK文件不存在，請先編譯"
        fi
        ;;
    *)
        echo "❌ 無效選擇"
        exit 1
        ;;
esac

echo ""
echo "🎯 快速參考:"
echo "- 項目文檔: PROJECT_DOCUMENTATION.md"
echo "- 快速指南: QUICK_START_GUIDE.md"
echo "- 存檔位置: /storage/emulated/0/Pictures/ErrorAnalysis/"
echo ""
echo "💡 提示: 大部分情況下使用選項1(快速編譯)即可"
