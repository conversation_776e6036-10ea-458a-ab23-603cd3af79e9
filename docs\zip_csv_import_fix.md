# ZIP檔案中CSV匯入功能修復

## 問題描述

用戶創建了多個CSV檔案（數學-代數基礎.csv、物理_力學基礎.csv、English-Basic_Grammar.csv），但當打包成ZIP檔案匯入時，系統報錯"找不到questions.json"，而實際上應該直接讀取ZIP中的所有CSV檔案。

## 修復內容

### 1. 更新ZIP匯入邏輯

**修復前**：
```kotlin
// 只尋找questions.json
val jsonFile = File(tempDir, "questions.json")
if (!jsonFile.exists()) {
    return ImportResult.Error("ZIP檔案中找不到questions.json")
}
```

**修復後**：
```kotlin
// 優先尋找CSV檔案
val csvFiles = tempDir.listFiles { file -> 
    file.isFile && file.name.endsWith(".csv", ignoreCase = true)
}?.toList() ?: emptyList()

if (csvFiles.isNotEmpty()) {
    return importMultipleCsvFiles(csvFiles, File(tempDir, "images"))
}

// 向後兼容：如果沒有CSV，尋找questions.json
val jsonFile = File(tempDir, "questions.json")
if (jsonFile.exists()) {
    return parseAndImport(jsonFile, File(tempDir, "images"))
}
```

### 2. 實現多CSV檔案匯入

新增`importMultipleCsvFiles`方法：
- 逐個處理ZIP中的所有CSV檔案
- 每個CSV檔案創建獨立的卡組
- 合併匯入結果和錯誤信息
- 提供詳細的處理狀態

```kotlin
private suspend fun importMultipleCsvFiles(csvFiles: List<File>, imagesDir: File?): ImportResult {
    val allResults = mutableListOf<ImportResult.Success>()
    val allErrors = mutableListOf<String>()
    
    for (csvFile in csvFiles) {
        val result = csvFile.inputStream().use { inputStream ->
            importFromCsv(inputStream, csvFile.name)
        }
        
        when (result) {
            is ImportResult.Success -> allResults.add(result)
            is ImportResult.Error -> allErrors.add("檔案 ${csvFile.name}：${result.message}")
        }
    }
    
    // 處理和合併結果...
}
```

### 3. 更新用戶界面說明

**新的說明文字**：
```
📋 支援的檔案格式：
• CSV檔案（推薦）- 直接匯入，檔名作為卡組名稱
• ZIP壓縮包（包含多個CSV檔案和images資料夾）
• JSON檔案（純文字題目）

📁 檔案結構範例：

單一CSV檔案：
數學-代數基礎.csv

ZIP壓縮包：
題庫包.zip
├── 數學-代數基礎.csv
├── 物理-力學基礎.csv
├── English-Basic_Grammar.csv
└── images/
    ├── diagram1.jpg
    └── formula2.png

💡 推薦使用ZIP格式批次匯入多個科目
```

## 支援的檔案結構

### 1. 單一CSV檔案
```
數學-代數基礎.csv
```
- 直接匯入單個科目
- 檔案名稱成為卡組名稱

### 2. ZIP包含多個CSV檔案
```
題庫包.zip
├── 數學-代數基礎.csv
├── 物理-力學基礎.csv
├── English-Basic_Grammar.csv
└── images/
    ├── equation.jpg
    ├── force_diagram.jpg
    └── grammar_chart.png
```
- 一次匯入多個科目
- 每個CSV檔案創建獨立卡組
- 共享images目錄中的圖片

### 3. ZIP包含JSON檔案（向後兼容）
```
舊格式題庫.zip
├── questions.json
└── images/
    └── ...
```
- 支援舊的JSON格式
- 保持向後兼容性

## CSV檔案格式

### 標準格式
```csv
題目ID,題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
A001,解方程式 x + 5 = 12,,x = 7,,一元一次方程式,簡單,基本移項法則
P001,質量為2kg的物體受到10N的力，求加速度,force.jpg,5m/s²,calc.jpg,牛頓第二定律,簡單,F = ma
E001,Choose: I ___ to school every day.,,go,,現在式,簡單,一般現在式的用法
```

### 必要欄位
- **題目ID**：唯一識別碼
- **答案**：題目答案

### 可選欄位
- **題目文字**：題目內容
- **題目圖片**：圖片檔名（放在images目錄）
- **答案圖片**：答案圖片檔名
- **標籤**：分類標籤
- **難度**：簡單/普通/困難
- **說明**：解題說明

## 匯入結果處理

### 1. 單一檔案成功
```
✅ 匯入成功
📚 已建立卡組：數學-代數基礎
🆔 卡組ID：deck_12345
📊 總題目數：10
✅ 成功匯入：10 張卡片
❌ 失敗數量：0
```

### 2. 多檔案部分成功
```
✅ 批次匯入完成
📚 已建立卡組：批次匯入結果
📊 總題目數：30
✅ 成功匯入：25 張卡片
❌ 失敗數量：5

錯誤詳情：
檔案 物理-力學基礎.csv：第3行答案不能為空
檔案 English-Basic_Grammar.csv：第7行欄位數量不匹配
```

### 3. 完全失敗
```
❌ 匯入失敗：所有CSV檔案匯入失敗

錯誤詳情：
檔案 數學-代數基礎.csv：CSV缺少必要欄位：答案
檔案 物理-力學基礎.csv：檔案編碼錯誤
檔案 English-Basic_Grammar.csv：檔案格式錯誤
```

## 錯誤處理改進

### 1. 檔案類型檢測
```kotlin
when {
    fileName.endsWith(".zip") -> importFromZip(inputStream)
    fileName.endsWith(".json") -> importFromJson(inputStream)
    fileName.endsWith(".csv") -> importFromCsv(inputStream, fileName)
    else -> ImportResult.Error("不支援的檔案格式，請使用ZIP、JSON或CSV檔案")
}
```

### 2. 詳細錯誤信息
```kotlin
return ImportResult.Error(
    "ZIP檔案中找不到可匯入的檔案\n\n" +
    "支援的檔案：\n" +
    "• CSV檔案（推薦）\n" +
    "• questions.json（舊格式）\n\n" +
    "請確認ZIP檔案包含正確的檔案格式"
)
```

### 3. 逐檔案錯誤追蹤
- 記錄每個檔案的處理狀態
- 提供具體的錯誤位置和原因
- 不因單一檔案失敗而中止整個匯入

## 使用指南

### 1. 準備檔案
1. 創建CSV檔案，使用UTF-8編碼
2. 確保包含必要欄位（題目ID、答案）
3. 如有圖片，放在images目錄下
4. 將所有檔案打包成ZIP

### 2. 匯入步驟
1. 打開錯題庫應用程式
2. 點擊「批次匯入」功能
3. 選擇ZIP檔案或單一CSV檔案
4. 等待匯入完成
5. 查看匯入結果和錯誤報告

### 3. 最佳實踐
- 使用描述性的檔案名稱
- 保持CSV格式一致性
- 測試小批量檔案後再大量匯入
- 備份原始檔案

## 技術優勢

### 1. 靈活性
- 支援單一CSV和批次ZIP匯入
- 向後兼容JSON格式
- 自動檢測檔案類型

### 2. 可靠性
- 逐檔案錯誤處理
- 詳細的錯誤報告
- 部分成功時的結果保存

### 3. 用戶體驗
- 清晰的進度提示
- 詳細的結果報告
- 友好的錯誤信息

這個修復徹底解決了ZIP檔案中CSV匯入的問題，現在用戶可以將多個CSV檔案打包成ZIP進行批次匯入！
