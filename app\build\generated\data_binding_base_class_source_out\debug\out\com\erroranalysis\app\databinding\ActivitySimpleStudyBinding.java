// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySimpleStudyBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final FloatingActionButton fabCreateDeck;

  @NonNull
  public final LinearLayout layoutEmpty;

  @NonNull
  public final RecyclerView recyclerDecks;

  @NonNull
  public final Toolbar toolbar;

  private ActivitySimpleStudyBinding(@NonNull CoordinatorLayout rootView,
      @NonNull FloatingActionButton fabCreateDeck, @NonNull LinearLayout layoutEmpty,
      @NonNull RecyclerView recyclerDecks, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.fabCreateDeck = fabCreateDeck;
    this.layoutEmpty = layoutEmpty;
    this.recyclerDecks = recyclerDecks;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySimpleStudyBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySimpleStudyBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_simple_study, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySimpleStudyBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.fab_create_deck;
      FloatingActionButton fabCreateDeck = ViewBindings.findChildViewById(rootView, id);
      if (fabCreateDeck == null) {
        break missingId;
      }

      id = R.id.layout_empty;
      LinearLayout layoutEmpty = ViewBindings.findChildViewById(rootView, id);
      if (layoutEmpty == null) {
        break missingId;
      }

      id = R.id.recycler_decks;
      RecyclerView recyclerDecks = ViewBindings.findChildViewById(rootView, id);
      if (recyclerDecks == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivitySimpleStudyBinding((CoordinatorLayout) rootView, fabCreateDeck,
          layoutEmpty, recyclerDecks, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
