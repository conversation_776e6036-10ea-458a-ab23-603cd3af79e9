{"logs": [{"outputFile": "com.erroranalysis.app-mergeDebugResources-43:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d3e25ec97e005582fa7c282bb4a9e4a2\\transformed\\appcompat-1.6.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,915,1006,1099,1193,1287,1387,1480,1575,1669,1760,1851,1935,2044,2148,2246,2356,2456,2563,2722,2821", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "216,323,430,513,618,734,824,910,1001,1094,1188,1282,1382,1475,1570,1664,1755,1846,1930,2039,2143,2241,2351,2451,2558,2717,2816,2898"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,435,542,649,732,837,953,1043,1129,1220,1313,1407,1501,1601,1694,1789,1883,1974,2065,2149,2258,2362,2460,2570,2670,2777,2936,11352", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "430,537,644,727,832,948,1038,1124,1215,1308,1402,1496,1596,1689,1784,1878,1969,2060,2144,2253,2357,2455,2565,2665,2772,2931,3030,11429"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\38790647496f2de6b4f9690090db3cca\\transformed\\material-1.11.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,345,422,500,591,676,778,893,976,1040,1129,1196,1256,1350,1414,1477,1533,1603,1670,1725,1844,1901,1965,2019,2092,2214,2297,2382,2514,2592,2672,2794,2880,2964,3024,3076,3142,3212,3285,3367,3444,3516,3593,3665,3735,3848,3941,4014,4104,4197,4271,4343,4434,4488,4568,4634,4718,4803,4865,4929,4992,5058,5163,5268,5363,5464,5528,5584", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,75,76,77,90,84,101,114,82,63,88,66,59,93,63,62,55,69,66,54,118,56,63,53,72,121,82,84,131,77,79,121,85,83,59,51,65,69,72,81,76,71,76,71,69,112,92,72,89,92,73,71,90,53,79,65,83,84,61,63,62,65,104,104,94,100,63,55,79", "endOffsets": "264,340,417,495,586,671,773,888,971,1035,1124,1191,1251,1345,1409,1472,1528,1598,1665,1720,1839,1896,1960,2014,2087,2209,2292,2377,2509,2587,2667,2789,2875,2959,3019,3071,3137,3207,3280,3362,3439,3511,3588,3660,3730,3843,3936,4009,4099,4192,4266,4338,4429,4483,4563,4629,4713,4798,4860,4924,4987,5053,5158,5263,5358,5459,5523,5579,5659"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3035,3111,3188,3266,3357,4174,4276,4391,6664,6728,6817,6884,6944,7038,7102,7165,7221,7291,7358,7413,7532,7589,7653,7707,7780,7902,7985,8070,8202,8280,8360,8482,8568,8652,8712,8764,8830,8900,8973,9055,9132,9204,9281,9353,9423,9536,9629,9702,9792,9885,9959,10031,10122,10176,10256,10322,10406,10491,10553,10617,10680,10746,10851,10956,11051,11152,11216,11272", "endLines": "5,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "endColumns": "12,75,76,77,90,84,101,114,82,63,88,66,59,93,63,62,55,69,66,54,118,56,63,53,72,121,82,84,131,77,79,121,85,83,59,51,65,69,72,81,76,71,76,71,69,112,92,72,89,92,73,71,90,53,79,65,83,84,61,63,62,65,104,104,94,100,63,55,79", "endOffsets": "314,3106,3183,3261,3352,3437,4271,4386,4469,6723,6812,6879,6939,7033,7097,7160,7216,7286,7353,7408,7527,7584,7648,7702,7775,7897,7980,8065,8197,8275,8355,8477,8563,8647,8707,8759,8825,8895,8968,9050,9127,9199,9276,9348,9418,9531,9624,9697,9787,9880,9954,10026,10117,10171,10251,10317,10401,10486,10548,10612,10675,10741,10846,10951,11046,11147,11211,11267,11347"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2087ae0d0678f91d06664b94633b22db\\transformed\\core-1.12.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,127", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3442,3544,3647,3752,3857,3956,4060,11434", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "3539,3642,3747,3852,3951,4055,4169,11530"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a1d9ffc143cbf5d78f49ada17a4468f7\\transformed\\play-services-basement-18.1.0\\res\\values-si\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "134", "endOffsets": "329"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5453", "endColumns": "138", "endOffsets": "5587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3cc10637bfd0587c1c332e1f64a0d224\\transformed\\play-services-base-18.1.0\\res\\values-si\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,447,567,676,814,934,1046,1140,1287,1398,1550,1677,1817,1974,2043,2100", "endColumns": "103,149,119,108,137,119,111,93,146,110,151,126,139,156,68,56,75", "endOffsets": "296,446,566,675,813,933,1045,1139,1286,1397,1549,1676,1816,1973,2042,2099,2175"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4474,4582,4736,4860,4973,5115,5239,5355,5592,5743,5858,6014,6145,6289,6450,6523,6584", "endColumns": "107,153,123,112,141,123,115,97,150,114,155,130,143,160,72,60,79", "endOffsets": "4577,4731,4855,4968,5110,5234,5350,5448,5738,5853,6009,6140,6284,6445,6518,6579,6659"}}]}]}