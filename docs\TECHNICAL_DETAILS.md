# 🔧 技術實現詳細文檔

## 📋 開發環境配置

### 1. 必要依賴
```gradle
dependencies {
    // Room數據庫
    implementation "androidx.room:room-runtime:2.5.0"
    implementation "androidx.room:room-ktx:2.5.0"
    kapt "androidx.room:room-compiler:2.5.0"
    
    // 協程
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'
    
    // ViewModel和LiveData
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.6.2'
    
    // Material Design
    implementation 'com.google.android.material:material:1.9.0'
    
    // Gson（JSON解析）
    implementation 'com.google.code.gson:gson:2.10.1'
}
```

### 2. 權限配置
```xml
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

### 3. 架構支援
```gradle
android {
    defaultConfig {
        ndk {
            abiFilters 'arm64-v8a'  // 只支援arm64，優化APK大小
        }
    }
}
```

## 🖼️ 圖片縮放核心實現

### 1. 簡單雙指縮放算法

```kotlin
/**
 * 處理簡單的雙指縮放
 */
private fun handleSimpleScale(event: MotionEvent, actionMasked: Int) {
    val (centerX, centerY) = getCenterPoint(event)
    
    when (actionMasked) {
        MotionEvent.ACTION_POINTER_DOWN -> {
            // 第二根手指按下，開始縮放
            val imageSpan = getImageSpanAtPosition(centerX, centerY)
            if (imageSpan != null) {
                currentImageSpan = imageSpan
                originalImageSize = imageSpan.drawable.intrinsicWidth to imageSpan.drawable.intrinsicHeight
                currentScale = getCurrentScale(imageSpan)
                lastDistance = getDistance(event)
                isScaling = true
                showToast("🎯 開始縮放圖片！")
            }
        }
        
        MotionEvent.ACTION_MOVE -> {
            if (isScaling && currentImageSpan != null && originalImageSize != null) {
                val currentDistance = getDistance(event)
                if (lastDistance > 0 && currentDistance > 0) {
                    val scaleFactor = currentDistance / lastDistance
                    val newScale = currentScale * scaleFactor
                    val clampedScale = kotlin.math.max(minScale, kotlin.math.min(newScale, maxScale))
                    
                    if (clampedScale != currentScale) {
                        currentScale = clampedScale
                        val (originalWidth, originalHeight) = originalImageSize!!
                        val newWidth = (originalWidth * currentScale).toInt()
                        val newHeight = (originalHeight * currentScale).toInt()
                        
                        currentImageSpan!!.drawable.setBounds(0, 0, newWidth, newHeight)
                        invalidate()
                    }
                    
                    lastDistance = currentDistance
                }
            }
        }
        
        MotionEvent.ACTION_POINTER_UP -> {
            // 手指抬起，結束縮放
            isScaling = false
        }
    }
}
```

### 2. 距離計算方法

```kotlin
/**
 * 計算兩指之間的距離
 */
private fun getDistance(event: MotionEvent): Float {
    if (event.pointerCount < 2) return 0f
    
    val x = event.getX(0) - event.getX(1)
    val y = event.getY(0) - event.getY(1)
    return kotlin.math.sqrt(x * x + y * y)
}

/**
 * 獲取兩指的中心點
 */
private fun getCenterPoint(event: MotionEvent): Pair<Float, Float> {
    if (event.pointerCount < 2) return event.x to event.y
    
    val centerX = (event.getX(0) + event.getX(1)) / 2
    val centerY = (event.getY(0) + event.getY(1)) / 2
    return centerX to centerY
}
```

## 📊 圖文混合內容處理

### 1. JSON格式定義

```json
[
  {"type": "text", "content": "這是文字內容"},
  {"type": "image", "content": "uuid-filename.jpg"},
  {"type": "text", "content": "更多文字"}
]
```

### 2. 內容解析實現

```kotlin
/**
 * 從JSON恢復圖文混合內容
 */
fun setRichContent(jsonContent: String) {
    try {
        // 檢查是否為JSON格式
        if (!jsonContent.trim().startsWith("[")) {
            setText(jsonContent)
            return
        }
        
        val content = com.google.gson.Gson().fromJson(
            jsonContent,
            Array<ContentItem>::class.java
        ).toList()
        
        val spannableBuilder = SpannableStringBuilder()
        
        for (item in content) {
            when (item.type) {
                "text" -> {
                    spannableBuilder.append(item.content)
                }
                "image" -> {
                    val bitmap = loadImageSafely(item.content)
                    if (bitmap != null) {
                        val drawable = BitmapDrawable(resources, bitmap)
                        drawable.setBounds(0, 0, bitmap.width, bitmap.height)
                        
                        val imageSpan = ZoomableImageSpan(drawable, item.content)
                        val start = spannableBuilder.length
                        spannableBuilder.append(IMAGE_PLACEHOLDER)
                        spannableBuilder.setSpan(
                            imageSpan,
                            start,
                            start + IMAGE_PLACEHOLDER.length,
                            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    }
                }
            }
        }
        
        setText(spannableBuilder)
    } catch (e: Exception) {
        setText(jsonContent)
    }
}
```

### 3. 內容生成實現

```kotlin
/**
 * 獲取圖文混合內容的JSON格式
 */
fun getRichContent(): String {
    val text = text as? Spannable ?: return text.toString()
    val content = mutableListOf<ContentItem>()
    
    val imageSpans = text.getSpans(0, text.length, ZoomableImageSpan::class.java)
    
    var lastIndex = 0
    for (imageSpan in imageSpans) {
        val start = text.getSpanStart(imageSpan)
        val end = text.getSpanEnd(imageSpan)
        
        // 添加圖片前的文字
        if (start > lastIndex) {
            val textContent = text.substring(lastIndex, start)
            if (textContent.isNotEmpty()) {
                content.add(ContentItem("text", textContent))
            }
        }
        
        // 添加圖片
        content.add(ContentItem("image", imageSpan.imageFileName ?: ""))
        
        lastIndex = end
    }
    
    // 添加最後的文字
    if (lastIndex < text.length) {
        val textContent = text.substring(lastIndex)
        if (textContent.isNotEmpty()) {
            content.add(ContentItem("text", textContent))
        }
    }
    
    return com.google.gson.Gson().toJson(content)
}
```

## 🎯 圖片檢測算法

### 1. 位置檢測實現

```kotlin
/**
 * 獲取指定位置的ImageSpan
 */
private fun getImageSpanAtPosition(x: Float, y: Float): ZoomableImageSpan? {
    try {
        val text = text as? Spannable ?: return null
        val layout = layout ?: return null
        
        // 獲取所有圖片
        val allImageSpans = text.getSpans(0, text.length, ZoomableImageSpan::class.java)
        if (allImageSpans.isEmpty()) return null
        
        val adjustedY = (y + scrollY).toInt()
        val line = layout.getLineForVertical(adjustedY)
        val offset = layout.getOffsetForHorizontal(line, x)
        
        // 檢查每個圖片的位置
        for ((index, imageSpan) in allImageSpans.withIndex()) {
            val start = text.getSpanStart(imageSpan)
            val end = text.getSpanEnd(imageSpan)
            
            // 擴大檢測範圍，包括圖片前後的位置
            val expandedStart = maxOf(0, start - 2)
            val expandedEnd = minOf(text.length, end + 2)
            
            // 檢查點擊是否在擴大的圖片範圍內
            if (offset >= expandedStart && offset <= expandedEnd) {
                return imageSpan
            }
        }
        
        return null
    } catch (e: Exception) {
        return null
    }
}
```

## 🔍 調試和測試

### 1. Toast調試系統

```kotlin
/**
 * 在手機上顯示提示訊息
 */
private fun showToast(message: String) {
    android.widget.Toast.makeText(context, message, android.widget.Toast.LENGTH_SHORT).show()
}
```

**調試提示訊息**：
- "🔥 強制檢測：X 指！" - 多指觸控檢測
- "🎯 開始縮放圖片！" - 縮放開始
- "📏 縮放: X.Xx" - 實時縮放比例
- "✅ 縮放結束: X.Xx" - 縮放結束
- "找到圖片 X！" - 圖片檢測成功

### 2. 日誌系統

```kotlin
android.util.Log.d("RichTextEditText", "訊息")
android.util.Log.d("CardView", "訊息")
android.util.Log.d("DeckDetail", "訊息")
```

### 3. 查看日誌方法

**方法1：Android Studio Logcat**
```bash
# 過濾特定標籤
adb logcat | grep "RichTextEditText"
```

**方法2：手機應用**
- LogCat Reader
- aLogcat
- Logcat Extreme

## 📈 性能優化

### 1. 圖片優化

```kotlin
/**
 * 安全載入圖片
 */
private fun loadImageSafely(filename: String): Bitmap? {
    return try {
        ImageStorageManager.getInstance(context).loadImage(filename)
    } catch (e: Exception) {
        android.util.Log.e("RichTextEditText", "載入圖片失敗: $filename", e)
        null
    }
}
```

### 2. 記憶體管理

```kotlin
/**
 * 獲取圖片當前的縮放比例
 */
private fun getCurrentScale(imageSpan: ZoomableImageSpan): Float {
    val currentWidth = imageSpan.drawable.bounds.width()
    val originalWidth = imageSpan.drawable.intrinsicWidth
    return if (originalWidth > 0) {
        currentWidth.toFloat() / originalWidth.toFloat()
    } else {
        1.0f
    }
}
```

### 3. APK大小優化

```gradle
android {
    defaultConfig {
        ndk {
            abiFilters 'arm64-v8a'  // 只支援arm64
        }
    }
    
    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}
```

## 🚨 常見問題解決

### 1. 圖片縮放不工作

**問題**：ScaleGestureDetector不穩定
**解決**：使用簡單雙指距離計算

### 2. 多指觸控檢測失敗

**問題**：觸控事件被攔截
**解決**：在onTouchEvent中優先處理多指事件

### 3. 圖片顯示異常

**問題**：drawable.setBounds()不生效
**解決**：確保調用invalidate()重繪

### 4. JSON解析失敗

**問題**：格式不正確
**解決**：添加try-catch降級為純文字

## 🔧 關鍵配置

### 1. 縮放參數

```kotlin
private val minScale = 0.5f    // 最小縮放比例
private val maxScale = 3.0f    // 最大縮放比例
private val IMAGE_PLACEHOLDER = "\uFFFC"  // 圖片佔位符
```

### 2. 觸控檢測

```kotlin
private var lastDistance = 0f     // 上次雙指距離
private var isScaling = false     // 是否正在縮放
private var currentImageSpan: ZoomableImageSpan? = null  // 當前縮放的圖片
```

### 3. 數據結構

```kotlin
private data class ContentItem(
    val type: String,    // "text" 或 "image"
    val content: String  // 文字內容或圖片文件名
)
```
