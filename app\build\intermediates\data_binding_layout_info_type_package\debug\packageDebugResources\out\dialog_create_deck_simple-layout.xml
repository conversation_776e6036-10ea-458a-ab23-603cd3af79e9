<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_create_deck_simple" modulePackage="com.erroranalysis.app" filePath="app\src\main\res\layout\dialog_create_deck_simple.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_create_deck_simple_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="84" endOffset="14"/></Target><Target id="@+id/edit_deck_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="22" startOffset="8" endLine="27" endOffset="34"/></Target><Target id="@+id/edit_subject" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="37" startOffset="8" endLine="42" endOffset="34"/></Target><Target id="@+id/edit_description" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="52" startOffset="8" endLine="57" endOffset="34"/></Target><Target id="@+id/button_cancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="68" startOffset="8" endLine="74" endOffset="31"/></Target><Target id="@+id/button_create" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="76" startOffset="8" endLine="80" endOffset="31"/></Target></Targets></Layout>