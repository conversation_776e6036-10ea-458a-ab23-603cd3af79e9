// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogCreateDeckBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton buttonCancel;

  @NonNull
  public final MaterialButton buttonCreate;

  @NonNull
  public final MaterialCardView cardPreview;

  @NonNull
  public final TextInputEditText editDeckName;

  @NonNull
  public final TextInputEditText editDescription;

  @NonNull
  public final TextInputEditText editSubject;

  @NonNull
  public final TextInputEditText editTags;

  @NonNull
  public final ScrollView layoutCustom;

  @NonNull
  public final LinearLayout layoutQuick;

  @NonNull
  public final RecyclerView recyclerColors;

  @NonNull
  public final RecyclerView recyclerExamTemplates;

  @NonNull
  public final RecyclerView recyclerQuickTemplates;

  @NonNull
  public final RecyclerView recyclerStyleTemplates;

  @NonNull
  public final RecyclerView recyclerSubjectTemplates;

  @NonNull
  public final TabLayout tabLayout;

  @NonNull
  public final TextView textPreviewCount;

  @NonNull
  public final TextView textPreviewIcon;

  @NonNull
  public final TextView textPreviewName;

  private DialogCreateDeckBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton buttonCancel, @NonNull MaterialButton buttonCreate,
      @NonNull MaterialCardView cardPreview, @NonNull TextInputEditText editDeckName,
      @NonNull TextInputEditText editDescription, @NonNull TextInputEditText editSubject,
      @NonNull TextInputEditText editTags, @NonNull ScrollView layoutCustom,
      @NonNull LinearLayout layoutQuick, @NonNull RecyclerView recyclerColors,
      @NonNull RecyclerView recyclerExamTemplates, @NonNull RecyclerView recyclerQuickTemplates,
      @NonNull RecyclerView recyclerStyleTemplates, @NonNull RecyclerView recyclerSubjectTemplates,
      @NonNull TabLayout tabLayout, @NonNull TextView textPreviewCount,
      @NonNull TextView textPreviewIcon, @NonNull TextView textPreviewName) {
    this.rootView = rootView;
    this.buttonCancel = buttonCancel;
    this.buttonCreate = buttonCreate;
    this.cardPreview = cardPreview;
    this.editDeckName = editDeckName;
    this.editDescription = editDescription;
    this.editSubject = editSubject;
    this.editTags = editTags;
    this.layoutCustom = layoutCustom;
    this.layoutQuick = layoutQuick;
    this.recyclerColors = recyclerColors;
    this.recyclerExamTemplates = recyclerExamTemplates;
    this.recyclerQuickTemplates = recyclerQuickTemplates;
    this.recyclerStyleTemplates = recyclerStyleTemplates;
    this.recyclerSubjectTemplates = recyclerSubjectTemplates;
    this.tabLayout = tabLayout;
    this.textPreviewCount = textPreviewCount;
    this.textPreviewIcon = textPreviewIcon;
    this.textPreviewName = textPreviewName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogCreateDeckBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogCreateDeckBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_create_deck, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogCreateDeckBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_cancel;
      MaterialButton buttonCancel = ViewBindings.findChildViewById(rootView, id);
      if (buttonCancel == null) {
        break missingId;
      }

      id = R.id.button_create;
      MaterialButton buttonCreate = ViewBindings.findChildViewById(rootView, id);
      if (buttonCreate == null) {
        break missingId;
      }

      id = R.id.card_preview;
      MaterialCardView cardPreview = ViewBindings.findChildViewById(rootView, id);
      if (cardPreview == null) {
        break missingId;
      }

      id = R.id.edit_deck_name;
      TextInputEditText editDeckName = ViewBindings.findChildViewById(rootView, id);
      if (editDeckName == null) {
        break missingId;
      }

      id = R.id.edit_description;
      TextInputEditText editDescription = ViewBindings.findChildViewById(rootView, id);
      if (editDescription == null) {
        break missingId;
      }

      id = R.id.edit_subject;
      TextInputEditText editSubject = ViewBindings.findChildViewById(rootView, id);
      if (editSubject == null) {
        break missingId;
      }

      id = R.id.edit_tags;
      TextInputEditText editTags = ViewBindings.findChildViewById(rootView, id);
      if (editTags == null) {
        break missingId;
      }

      id = R.id.layout_custom;
      ScrollView layoutCustom = ViewBindings.findChildViewById(rootView, id);
      if (layoutCustom == null) {
        break missingId;
      }

      id = R.id.layout_quick;
      LinearLayout layoutQuick = ViewBindings.findChildViewById(rootView, id);
      if (layoutQuick == null) {
        break missingId;
      }

      id = R.id.recycler_colors;
      RecyclerView recyclerColors = ViewBindings.findChildViewById(rootView, id);
      if (recyclerColors == null) {
        break missingId;
      }

      id = R.id.recycler_exam_templates;
      RecyclerView recyclerExamTemplates = ViewBindings.findChildViewById(rootView, id);
      if (recyclerExamTemplates == null) {
        break missingId;
      }

      id = R.id.recycler_quick_templates;
      RecyclerView recyclerQuickTemplates = ViewBindings.findChildViewById(rootView, id);
      if (recyclerQuickTemplates == null) {
        break missingId;
      }

      id = R.id.recycler_style_templates;
      RecyclerView recyclerStyleTemplates = ViewBindings.findChildViewById(rootView, id);
      if (recyclerStyleTemplates == null) {
        break missingId;
      }

      id = R.id.recycler_subject_templates;
      RecyclerView recyclerSubjectTemplates = ViewBindings.findChildViewById(rootView, id);
      if (recyclerSubjectTemplates == null) {
        break missingId;
      }

      id = R.id.tab_layout;
      TabLayout tabLayout = ViewBindings.findChildViewById(rootView, id);
      if (tabLayout == null) {
        break missingId;
      }

      id = R.id.text_preview_count;
      TextView textPreviewCount = ViewBindings.findChildViewById(rootView, id);
      if (textPreviewCount == null) {
        break missingId;
      }

      id = R.id.text_preview_icon;
      TextView textPreviewIcon = ViewBindings.findChildViewById(rootView, id);
      if (textPreviewIcon == null) {
        break missingId;
      }

      id = R.id.text_preview_name;
      TextView textPreviewName = ViewBindings.findChildViewById(rootView, id);
      if (textPreviewName == null) {
        break missingId;
      }

      return new DialogCreateDeckBinding((LinearLayout) rootView, buttonCancel, buttonCreate,
          cardPreview, editDeckName, editDescription, editSubject, editTags, layoutCustom,
          layoutQuick, recyclerColors, recyclerExamTemplates, recyclerQuickTemplates,
          recyclerStyleTemplates, recyclerSubjectTemplates, tabLayout, textPreviewCount,
          textPreviewIcon, textPreviewName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
