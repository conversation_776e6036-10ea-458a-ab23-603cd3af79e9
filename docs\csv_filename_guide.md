# CSV檔名命名指南

## 檔名作為卡組名稱

App會自動將CSV檔案名稱作為卡組名稱，並進行智能處理和科目檢測。

## 命名規則

### 1. 基本格式
```
科目-主題.csv
科目_主題.csv
主題名稱.csv
```

### 2. 範例檔名
```
數學-代數基礎.csv          → 卡組名稱：數學 - 代數基礎
物理_力學基礎.csv          → 卡組名稱：物理 力學基礎
English-Basic_Grammar.csv  → 卡組名稱：English - Basic Grammar
微積分練習題.csv           → 卡組名稱：微積分練習題
高中化學_有機化合物.csv    → 卡組名稱：高中化學 有機化合物
```

## 自動處理功能

### 1. 檔名美化
- **底線轉空格**：`數學_代數` → `數學 代數`
- **連字號美化**：`數學-代數` → `數學 - 代數`
- **多空格合併**：`數學  代數` → `數學 代數`
- **去除前後空格**：自動清理

### 2. 科目自動檢測
App會根據檔名自動檢測科目，並設定對應的圖標和顏色：

#### 數學相關
- **關鍵字**：數學、math、代數、algebra、幾何、geometry、微積分、calculus、統計、statistics
- **圖標**：📘
- **顏色**：藍色 (#4A90E2)

#### 物理相關
- **關鍵字**：物理、physics、力學、mechanics、電磁、electromagnetic
- **圖標**：📗
- **顏色**：橙色 (#F5A623)

#### 化學相關
- **關鍵字**：化學、chemistry、有機、organic、無機、inorganic
- **圖標**：📙
- **顏色**：綠色 (#7ED321)

#### 生物相關
- **關鍵字**：生物、biology
- **圖標**：📕
- **顏色**：青色 (#50E3C2)

#### 語言相關
- **英文**：英文、english
  - 圖標：📔，顏色：紅色 (#D0021B)
- **國文**：國文、chinese
  - 圖標：📖，顏色：紫色 (#9013FE)

#### 其他科目
- **歷史**：歷史、history → 📜 棕色
- **地理**：地理、geography → 🗺️ 綠色
- **程式**：程式、programming、computer → 💻 橙紅色

## 命名建議

### 1. 清晰明確
```
✅ 好的檔名：
- 數學-二次方程式.csv
- 物理-電磁學基礎.csv
- 英文-時態練習.csv
- 化學-酸鹼反應.csv

❌ 不好的檔名：
- test.csv
- 123.csv
- 題目.csv
- data.csv
```

### 2. 層次結構
```
✅ 建議的命名方式：
- 數學-代數-一次方程式.csv
- 數學-代數-二次方程式.csv
- 數學-幾何-三角形.csv
- 物理-力學-牛頓定律.csv
- 物理-電磁-電路分析.csv
```

### 3. 版本管理
```
✅ 版本控制：
- 數學-代數基礎_v1.csv
- 數學-代數基礎_v2.csv
- 數學-代數基礎_2024.csv
- 數學-代數基礎_期中考.csv
```

### 4. 難度標示
```
✅ 難度區分：
- 數學-代數-基礎.csv
- 數學-代數-進階.csv
- 英文-文法-初級.csv
- 英文-文法-高級.csv
```

## 實際效果

### 檔名處理範例

| 原始檔名 | 處理後的卡組名稱 | 檢測到的科目 |
|---------|----------------|-------------|
| `數學-代數基礎.csv` | 數學 - 代數基礎 | 數學 📘 |
| `物理_力學基礎.csv` | 物理 力學基礎 | 物理 📗 |
| `English-Grammar.csv` | English - Grammar | 英文 📔 |
| `微積分練習題.csv` | 微積分練習題 | 數學 📘 |
| `高中化學_有機化合物.csv` | 高中化學 有機化合物 | 化學 📙 |
| `程式設計_基礎語法.csv` | 程式設計 基礎語法 | 程式 💻 |

### 降級處理
如果檔名不合適，系統會使用預設名稱：

| 問題檔名 | 處理結果 |
|---------|---------|
| `.csv` | CSV匯入題庫 |
| `a.csv` | CSV匯入題庫 |
| `   .csv` | CSV匯入題庫 |

## 最佳實踐

### 1. 檔案組織
```
題庫資料夾/
├── 數學-代數基礎.csv
├── 數學-幾何入門.csv
├── 物理-力學基礎.csv
├── 物理-電磁學.csv
├── 英文-基礎文法.csv
└── 英文-進階閱讀.csv
```

### 2. 命名一致性
- 同一科目使用相同的前綴
- 使用統一的分隔符（建議用連字號 `-`）
- 保持命名風格一致

### 3. 中英文混用
- 可以使用中英文混合命名
- 系統會正確識別兩種語言的科目關鍵字
- 建議在團隊內統一命名語言

### 4. 特殊字符避免
- 避免使用特殊字符：`/` `\` `:` `*` `?` `"` `<` `>` `|`
- 可以使用：`-` `_` `()` `[]` 中文字符

## 技術實現

### 檔名處理流程
```
原始檔名 → 移除副檔名 → 美化處理 → 科目檢測 → 最終卡組名稱
```

### 美化處理規則
```kotlin
val cleanName = nameWithoutExtension
    .replace("_", " ")           // 底線轉空格
    .replace("-", " - ")         // 連字號轉分隔符
    .replace(Regex("\\s+"), " ") // 多個空格合併
    .trim()                      // 去除前後空格
```

### 科目檢測邏輯
```kotlin
val lowerName = deckName.lowercase()
when {
    lowerName.contains("數學") || lowerName.contains("math") -> "數學"
    lowerName.contains("物理") || lowerName.contains("physics") -> "物理"
    // ... 其他科目檢測
}
```

這個檔名命名系統讓用戶能夠通過簡單的檔案命名就獲得完整的卡組管理功能！
