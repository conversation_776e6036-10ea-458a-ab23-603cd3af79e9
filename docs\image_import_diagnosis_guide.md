# 圖片匯入問題診斷指南

## 🔍 問題描述

用戶反映兩個問題：
1. **只要"題目文字"是空的，就不會被匯入**
2. **題目圖片也不會被載入到卡片的題目richtexteditor**

## 🛠️ 診斷步驟

### 步驟1：檢查CSV檔案格式

使用提供的測試檔案 `docs/test_image_import.csv`：

```csv
題目ID,題目文字,題目圖片,答案,標籤,難度
Q001,解這個方程式,math_eq1.jpg,x = 5,代數,簡單
Q002,,math_eq2.png,y = 3,代數,普通
Q003,分析這個圖表,diagram.jpg,見圖解,統計,困難
Q004,這是純文字題目,,42,基礎,簡單
Q005,,missing_image.jpg,找不到圖片,測試,普通
```

**重點測試案例**：
- **Q002**：題目文字為空，只有圖片 `math_eq2.png`
- **Q005**：題目文字為空，圖片檔案不存在

### 步驟2：查看詳細日誌

執行匯入後，使用以下命令查看詳細日誌：

```bash
adb logcat | grep "BatchImportManager"
```

**關鍵日誌信息**：

#### 欄位映射檢查
```
欄位映射: {questionText=1, questionImage=2, answer=3, tags=4, difficulty=5}
```

#### 題目解析詳情
```
=== 題目解析詳情 ===
CSV行值: [Q002, , math_eq2.png, y = 3, 代數, 普通]
questionText索引: 1
questionImage索引: 2
題目文字原始值: ''
題目圖片原始值: 'math_eq2.png'
解析結果 - 文字: '', 圖片: 'math_eq2.png'
```

#### getValue函數詳情
```
getValue: field='questionText', index=1, rawValue='', trimmedValue=''
getValue: field='questionImage', index=2, rawValue='math_eq2.png', trimmedValue='math_eq2.png'
```

#### 圖片處理過程
```
處理圖片：math_eq2.png
圖片目錄：/path/to/images
圖片完整路徑：/path/to/images/math_eq2.png
圖片檔案是否存在：true
圖片解碼成功：800x600
✅ 成功添加圖片：uuid-filename.jpg
```

### 步驟3：檢查匯入結果

#### 預期結果
- **Q001**：✅ 正常匯入（有文字+有圖片）
- **Q002**：✅ 應該匯入（只有圖片）
- **Q003**：✅ 正常匯入（有文字+有圖片）
- **Q004**：✅ 正常匯入（只有文字）
- **Q005**：✅ 應該匯入（圖片不存在，顯示佔位符）

#### 實際檢查
1. 進入新創建的卡組
2. 檢查卡片數量是否為5張
3. 逐一查看每張卡片內容

### 步驟4：檢查卡片顯示

#### RichTextEditor圖片載入檢查
```bash
adb logcat | grep "RichTextEditText"
```

**關鍵日誌**：
```
開始載入內容，長度: 45
圖文內容載入完成
安全載入圖片失敗: filename.jpg
```

## 🔧 可能的問題和解決方案

### 問題1：題目文字為空時不匯入

#### 可能原因
1. **驗證邏輯錯誤**：`createQuestionFromCsv`中的驗證邏輯拋出異常
2. **欄位映射錯誤**：`questionImage`欄位沒有正確映射
3. **CSV解析錯誤**：空欄位被解析為null而不是空字串

#### 診斷方法
查看日誌中的：
```
解析第X行失敗：至少需要題目文字或題目圖片其中一個
```

#### 解決方案
1. 確認CSV檔案格式正確
2. 檢查欄位名稱是否匹配（"題目圖片"）
3. 確認圖片檔案確實存在

### 問題2：圖片不顯示在RichTextEditor

#### 可能原因
1. **圖片保存失敗**：圖片沒有正確保存到應用程式存儲
2. **圖片載入失敗**：RichTextEditor無法載入圖片
3. **JSON格式錯誤**：圖文混合內容的JSON格式不正確
4. **圖片路徑錯誤**：圖片檔案名稱或路徑有問題

#### 診斷方法
1. **檢查圖片保存**：
   ```
   ✅ 成功添加圖片：uuid-filename.jpg
   ```

2. **檢查JSON格式**：
   ```
   [{"type":"image","content":"uuid-filename.jpg"}]
   ```

3. **檢查圖片載入**：
   ```
   安全載入圖片失敗: uuid-filename.jpg
   ```

#### 解決方案
1. 確認圖片檔案存在於ZIP的images目錄中
2. 檢查圖片格式是否支援（JPG、PNG）
3. 確認圖片檔案沒有損壞

### 問題3：ZIP檔案結構錯誤

#### 正確的ZIP結構
```
test_images.zip
├── test_image_import.csv
└── images/
    ├── math_eq1.jpg
    ├── math_eq2.png
    └── diagram.jpg
```

#### 常見錯誤
- 圖片不在`images/`目錄中
- 目錄名稱錯誤（如`image/`而不是`images/`）
- 圖片檔案名稱與CSV不匹配

## 📋 診斷檢查清單

### CSV檔案檢查
- [ ] 檔案編碼為UTF-8
- [ ] 標題行包含"題目圖片"欄位
- [ ] 空的題目文字欄位確實為空（不是空格）
- [ ] 圖片檔案名稱正確

### ZIP檔案檢查
- [ ] 包含`images/`目錄
- [ ] 圖片檔案存在於`images/`目錄中
- [ ] 圖片檔案名稱與CSV一致
- [ ] 圖片格式為JPG或PNG

### 匯入過程檢查
- [ ] 欄位映射正確識別"題目圖片"
- [ ] 只有圖片的題目沒有被跳過
- [ ] 圖片成功保存到應用程式存儲
- [ ] JSON格式正確生成

### 顯示檢查
- [ ] 卡片數量正確
- [ ] 只有圖片的卡片能正常顯示
- [ ] 圖片在RichTextEditor中正確載入
- [ ] 圖片可以正常縮放和查看

## 🎯 測試步驟

### 1. 準備測試檔案
1. 使用`docs/test_image_import.csv`
2. 創建3張測試圖片：
   - `math_eq1.jpg`
   - `math_eq2.png`
   - `diagram.jpg`
3. 打包成ZIP檔案

### 2. 執行匯入測試
1. 開啟批次匯入功能
2. 選擇ZIP檔案
3. 執行匯入

### 3. 檢查結果
1. 查看logcat日誌
2. 檢查卡組中的卡片數量
3. 逐一查看卡片內容
4. 特別檢查Q002（只有圖片）

### 4. 回報結果
如果問題仍然存在，請提供：
- 完整的logcat日誌
- 使用的CSV檔案內容
- ZIP檔案結構截圖
- 匯入結果截圖
- 卡片顯示截圖

## 📞 技術支援

如果按照此指南仍無法解決問題，請聯繫開發者並提供：

1. **詳細日誌**：
   ```bash
   adb logcat | grep "BatchImportManager\|RichTextEditText" > debug_log.txt
   ```

2. **測試檔案**：
   - 使用的CSV檔案
   - ZIP檔案結構
   - 圖片檔案列表

3. **問題描述**：
   - 具體哪個步驟失敗
   - 預期結果vs實際結果
   - 錯誤信息截圖

這個診斷指南將幫助快速定位和解決圖片匯入問題。
