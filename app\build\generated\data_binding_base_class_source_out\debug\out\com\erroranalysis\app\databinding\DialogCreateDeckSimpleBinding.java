// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogCreateDeckSimpleBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton buttonCancel;

  @NonNull
  public final MaterialButton buttonCreate;

  @NonNull
  public final TextInputEditText editDeckName;

  @NonNull
  public final TextInputEditText editDescription;

  @NonNull
  public final TextInputEditText editSubject;

  private DialogCreateDeckSimpleBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton buttonCancel, @NonNull MaterialButton buttonCreate,
      @NonNull TextInputEditText editDeckName, @NonNull TextInputEditText editDescription,
      @NonNull TextInputEditText editSubject) {
    this.rootView = rootView;
    this.buttonCancel = buttonCancel;
    this.buttonCreate = buttonCreate;
    this.editDeckName = editDeckName;
    this.editDescription = editDescription;
    this.editSubject = editSubject;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogCreateDeckSimpleBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogCreateDeckSimpleBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_create_deck_simple, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogCreateDeckSimpleBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_cancel;
      MaterialButton buttonCancel = ViewBindings.findChildViewById(rootView, id);
      if (buttonCancel == null) {
        break missingId;
      }

      id = R.id.button_create;
      MaterialButton buttonCreate = ViewBindings.findChildViewById(rootView, id);
      if (buttonCreate == null) {
        break missingId;
      }

      id = R.id.edit_deck_name;
      TextInputEditText editDeckName = ViewBindings.findChildViewById(rootView, id);
      if (editDeckName == null) {
        break missingId;
      }

      id = R.id.edit_description;
      TextInputEditText editDescription = ViewBindings.findChildViewById(rootView, id);
      if (editDescription == null) {
        break missingId;
      }

      id = R.id.edit_subject;
      TextInputEditText editSubject = ViewBindings.findChildViewById(rootView, id);
      if (editSubject == null) {
        break missingId;
      }

      return new DialogCreateDeckSimpleBinding((LinearLayout) rootView, buttonCancel, buttonCreate,
          editDeckName, editDescription, editSubject);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
