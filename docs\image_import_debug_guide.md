# 圖片匯入問題深度診斷指南

## 🔍 問題現狀

用戶反映：
1. **只有題目圖片無法匯入** - 題目文字為空，只有圖片的題目無法匯入
2. **有題目文字和題目圖片，圖片也無法被放入卡片** - 只有文字被放入，圖片消失

## 🛠️ 最新修復

### 1. 修復圖片目錄傳遞問題
- 移除了`@Suppress("UNUSED_PARAMETER")`標記
- 確保ZIP檔案中的`imagesDir`正確傳遞給CSV匯入函數

### 2. 增強圖片處理錯誤處理
- 改進空內容處理，確保即使圖片處理失敗也有佔位符
- 修復`optString`處理空字串的問題

### 3. 添加詳細診斷日誌
- 圖片目錄存在性檢查
- 圖片目錄內容列表
- 圖片處理每個步驟的詳細記錄

## 📱 詳細診斷步驟

### 步驟1：準備測試檔案

創建以下ZIP檔案結構：
```
test_images.zip
├── test_image_import.csv
└── images/
    ├── math_eq1.jpg
    ├── math_eq2.png
    └── diagram.jpg
```

**CSV內容**：
```csv
題目ID,題目文字,題目圖片,答案,標籤,難度
Q001,解這個方程式,math_eq1.jpg,x = 5,代數,簡單
Q002,,math_eq2.png,y = 3,代數,普通
Q003,分析這個圖表,diagram.jpg,見圖解,統計,困難
Q004,這是純文字題目,,42,基礎,簡單
Q005,,missing_image.jpg,找不到圖片,測試,普通
```

### 步驟2：執行匯入並收集日誌

1. **開啟日誌收集**：
   ```bash
   adb logcat -c  # 清除舊日誌
   adb logcat | grep "BatchImportManager" > import_debug.log &
   ```

2. **執行匯入**：
   - 開啟應用程式批次匯入功能
   - 選擇準備好的ZIP檔案
   - 執行匯入

3. **停止日誌收集**：
   ```bash
   # 按Ctrl+C停止日誌收集
   ```

### 步驟3：分析關鍵日誌

#### 3.1 圖片目錄檢查
查找以下日誌：
```
圖片目錄: /path/to/temp/images
圖片目錄是否存在: true
圖片目錄包含 3 個檔案:
  - math_eq1.jpg (12345 bytes)
  - math_eq2.png (23456 bytes)
  - diagram.jpg (34567 bytes)
```

**如果看到**：
- `圖片目錄是否存在: false` → ZIP檔案結構問題
- `圖片目錄包含 0 個檔案` → images目錄為空

#### 3.2 題目解析檢查
查找以下日誌：
```
=== 題目解析詳情 ===
欄位映射: {questionText=1, questionImage=2, answer=3, tags=4, difficulty=5}
CSV行值: [Q002, , math_eq2.png, y = 3, 代數, 普通]
questionText索引: 1
questionImage索引: 2
題目文字原始值: ''
題目圖片原始值: 'math_eq2.png'
解析結果 - 文字: '', 圖片: 'math_eq2.png'
```

**如果看到**：
- `questionImage索引: null` → 欄位映射失敗
- `題目圖片原始值: ''` → CSV檔案中圖片欄位為空

#### 3.3 parseQuestionToCard檢查
查找以下日誌：
```
=== parseQuestionToCard ===
questionText: ''
questionImageName: 'math_eq2.png'
answer: 'y = 3'
answerImageName: null
imagesDir: /path/to/temp/images
```

**如果看到**：
- `questionImageName: null` → 圖片名稱處理失敗
- `imagesDir: null` → 圖片目錄沒有正確傳遞

#### 3.4 圖片處理檢查
查找以下日誌：
```
處理圖片：math_eq2.png
圖片目錄：/path/to/temp/images
圖片完整路徑：/path/to/temp/images/math_eq2.png
圖片檔案是否存在：true
圖片檔案大小：23456 bytes
圖片解碼成功：800x600
✅ 成功添加圖片：uuid-filename.jpg
```

**如果看到**：
- `圖片檔案是否存在：false` → 圖片檔案不存在
- `圖片解碼失敗` → 圖片檔案損壞
- `圖片保存失敗` → 應用程式存儲問題

#### 3.5 JSON內容檢查
查找以下日誌：
```
✅ 構建圖文混合內容成功：[{"type":"image","content":"uuid-filename.jpg"}]
```

**如果看到**：
- `⚠️ 沒有任何內容（文字或圖片）` → 內容構建失敗
- `降級處理：返回圖片佔位符` → 圖片處理失敗但有佔位符

### 步驟4：檢查匯入結果

1. **檢查卡組**：
   - 進入新創建的卡組
   - 確認卡片數量（應該是5張）

2. **檢查卡片內容**：
   - Q001：應該有文字"解這個方程式" + 圖片
   - Q002：應該只有圖片（或圖片佔位符）
   - Q003：應該有文字"分析這個圖表" + 圖片
   - Q004：應該只有文字"這是純文字題目"
   - Q005：應該有圖片佔位符"[圖片檔案不存在: missing_image.jpg]"

3. **檢查RichTextEditor**：
   - 點擊編輯卡片
   - 確認圖片是否在編輯器中顯示
   - 檢查圖片是否可以縮放

## 🔧 常見問題和解決方案

### 問題1：圖片目錄不存在
**症狀**：`圖片目錄是否存在: false`
**原因**：ZIP檔案中沒有`images/`目錄
**解決**：確認ZIP檔案結構正確

### 問題2：圖片檔案不存在
**症狀**：`圖片檔案是否存在：false`
**原因**：圖片檔案名稱與CSV不匹配
**解決**：檢查檔案名稱大小寫和副檔名

### 問題3：欄位映射失敗
**症狀**：`questionImage索引: null`
**原因**：CSV標題行不包含"題目圖片"欄位
**解決**：確認CSV標題行正確

### 問題4：圖片解碼失敗
**症狀**：`圖片解碼失敗`
**原因**：圖片檔案損壞或格式不支援
**解決**：使用標準JPG或PNG格式

### 問題5：只有圖片的題目被跳過
**症狀**：`解析第X行失敗：至少需要題目文字或題目圖片其中一個`
**原因**：圖片名稱為空或圖片處理完全失敗
**解決**：檢查CSV檔案和圖片檔案

## 📋 診斷檢查清單

### ZIP檔案檢查
- [ ] ZIP檔案包含`images/`目錄
- [ ] 圖片檔案存在於`images/`目錄中
- [ ] 圖片檔案名稱與CSV一致
- [ ] 圖片格式為JPG或PNG
- [ ] 圖片檔案沒有損壞

### CSV檔案檢查
- [ ] 標題行包含"題目圖片"欄位
- [ ] 只有圖片的行，題目文字欄位為空
- [ ] 圖片檔案名稱正確填寫
- [ ] 檔案編碼為UTF-8

### 日誌檢查
- [ ] 圖片目錄存在且包含檔案
- [ ] 欄位映射正確識別圖片欄位
- [ ] 圖片檔案成功解碼
- [ ] 圖片成功保存到應用程式存儲
- [ ] JSON內容正確生成

### 結果檢查
- [ ] 卡片數量正確
- [ ] 只有圖片的卡片能顯示
- [ ] 圖片在編輯器中正確載入
- [ ] 圖片可以正常縮放

## 🎯 下一步行動

1. **立即測試**：使用提供的測試檔案執行匯入
2. **收集日誌**：按照步驟2收集完整的診斷日誌
3. **分析結果**：按照步驟3分析關鍵日誌信息
4. **回報問題**：如果問題仍存在，提供完整的日誌檔案

這個修復版本包含了大量的診斷信息，應該能夠幫助我們快速定位圖片匯入問題的根源。
