<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_batch_import" modulePackage="com.erroranalysis.app" filePath="app\src\main\res\layout\activity_batch_import.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_batch_import_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="173" endOffset="14"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="8" startOffset="4" endLine="15" endOffset="51"/></Target><Target id="@+id/text_instructions" view="TextView"><Expressions/><location startLine="46" startOffset="16" endLine="54" endOffset="52"/></Target><Target id="@+id/button_select_file" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="59" startOffset="12" endLine="71" endOffset="65"/></Target><Target id="@+id/button_show_example" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="74" startOffset="12" endLine="86" endOffset="80"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="89" startOffset="12" endLine="95" endOffset="43"/></Target><Target id="@+id/text_status" view="TextView"><Expressions/><location startLine="105" startOffset="16" endLine="113" endOffset="46"/></Target><Target id="@+id/text_errors" view="TextView"><Expressions/><location startLine="127" startOffset="16" endLine="135" endOffset="52"/></Target><Target id="@+id/button_view_deck" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="140" startOffset="12" endLine="152" endOffset="65"/></Target><Target id="@+id/button_complete" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="155" startOffset="12" endLine="167" endOffset="65"/></Target></Targets></Layout>