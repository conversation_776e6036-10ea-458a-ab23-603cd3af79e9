<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 按鈕區域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:gravity="end"
        android:orientation="horizontal">

        <ImageButton
            android:id="@+id/button_cancel"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="取消"
            android:src="@drawable/ic_close"
            android:tint="@color/text_secondary" />

        <ImageButton
            android:id="@+id/button_save"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="保存"
            android:src="@drawable/ic_check"
            android:tint="@color/primary" />

    </LinearLayout>

    <!-- 內容區域 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="500dp"
        android:fillViewport="true"
        android:scrollbars="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 題目區域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="題目"
                    android:textColor="@color/primary"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <ImageButton
                    android:id="@+id/button_add_question_image"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="添加題目圖片"
                    android:src="@drawable/ic_image"
                    android:tint="@color/primary" />

            </LinearLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_question"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="輸入題目內容"
                    android:inputType="textMultiLine"
                    android:maxLines="8"
                    android:minLines="3"
                    android:scrollbars="vertical"
                    android:textColor="@color/black"
                    android:textColorHint="@color/text_tertiary" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- 答案區域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="答案"
                    android:textColor="@color/primary"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <ImageButton
                    android:id="@+id/button_add_answer_image"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="添加答案圖片"
                    android:src="@drawable/ic_image"
                    android:tint="@color/primary" />

            </LinearLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_answer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="輸入答案內容"
                    android:inputType="textMultiLine"
                    android:maxLines="8"
                    android:minLines="3"
                    android:scrollbars="vertical"
                    android:textColor="@color/black"
                    android:textColorHint="@color/text_tertiary" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- 標籤區域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="標籤"
                android:textColor="@color/primary"
                android:textSize="14sp"
                android:textStyle="bold" />

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_tags"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="用逗號分隔多個標籤"
                    android:inputType="text"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textColorHint="@color/text_tertiary" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
