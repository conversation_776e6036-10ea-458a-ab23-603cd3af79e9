<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_card_viewer" modulePackage="com.erroranalysis.app" filePath="app\src\main\res\layout\activity_card_viewer.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_card_viewer_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="147" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="15" startOffset="8" endLine="22" endOffset="55"/></Target><Target id="@+id/tv_content_type" view="TextView"><Expressions/><location startLine="35" startOffset="8" endLine="43" endOffset="53"/></Target><Target id="@+id/tv_tap_hint" view="TextView"><Expressions/><location startLine="45" startOffset="8" endLine="54" endOffset="43"/></Target><Target id="@+id/content_frame" view="FrameLayout"><Expressions/><location startLine="59" startOffset="4" endLine="111" endOffset="17"/></Target><Target id="@+id/scroll_view" view="ScrollView"><Expressions/><location startLine="71" startOffset="8" endLine="101" endOffset="20"/></Target><Target id="@+id/tv_content" view="TextView"><Expressions/><location startLine="87" startOffset="16" endLine="97" endOffset="63"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="104" startOffset="8" endLine="109" endOffset="39"/></Target><Target id="@+id/btn_edit" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="122" startOffset="8" endLine="132" endOffset="51"/></Target><Target id="@+id/btn_toggle" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="134" startOffset="8" endLine="143" endOffset="58"/></Target></Targets></Layout>