# 🚀 開發指南

## 📋 快速開始

### 1. 環境要求
- **Android Studio**: Arctic Fox 或更新版本
- **Kotlin**: 1.8.0 或更新版本
- **Gradle**: 8.0 或更新版本
- **最低SDK**: API 24 (Android 7.0)
- **目標SDK**: API 34

### 2. 項目設置
```bash
# 克隆項目
git clone [項目地址]

# 打開Android Studio
# File -> Open -> 選擇項目目錄

# 同步Gradle
./gradlew sync

# 編譯項目
./gradlew assembleDebug
```

### 3. 關鍵文件位置
```
重要文件：
├── app/src/main/java/com/erroranalysis/app/ui/widgets/RichTextEditText.kt  # 核心組件
├── app/src/main/java/com/erroranalysis/app/ui/study/CardViewActivity.kt   # 卡片檢視
├── app/src/main/java/com/erroranalysis/app/ui/study/CardEditActivity.kt   # 卡片編輯
├── app/src/main/java/com/erroranalysis/app/utils/ImageStorageManager.kt   # 圖片管理
└── app/src/main/java/com/erroranalysis/app/data/DeckDataManager.kt        # 數據管理
```

## 🔧 核心組件開發

### 1. RichTextEditText 修改指南

**位置**: `app/src/main/java/com/erroranalysis/app/ui/widgets/RichTextEditText.kt`

**關鍵方法**:
```kotlin
// 圖片縮放核心邏輯
private fun handleSimpleScale(event: MotionEvent, actionMasked: Int)

// 圖片位置檢測
private fun getImageSpanAtPosition(x: Float, y: Float): ZoomableImageSpan?

// 內容設置和獲取
fun setRichContent(jsonContent: String)
fun getRichContent(): String
```

**修改注意事項**:
- 圖片縮放使用簡單雙指距離計算，不依賴ScaleGestureDetector
- 所有圖片操作都要調用`invalidate()`重繪
- 支援只讀模式，通過`setReadOnlyMode()`控制

### 2. 圖片處理開發

**ImageStorageManager 使用**:
```kotlin
val imageManager = ImageStorageManager.getInstance(context)

// 保存圖片
val filename = imageManager.saveImage(bitmap)

// 載入圖片
val bitmap = imageManager.loadImage(filename)

// 刪除圖片
imageManager.deleteImage(filename)
```

**圖片格式**:
- 文件名: UUID格式 (例: "abc123-def456.jpg")
- 存儲位置: 應用私有目錄
- 格式: JPEG, 質量80%

### 3. 數據模型開發

**卡片內容格式**:
```json
[
  {"type": "text", "content": "文字內容"},
  {"type": "image", "content": "圖片文件名.jpg"},
  {"type": "text", "content": "更多文字"}
]
```

**數據庫操作**:
```kotlin
// 獲取數據管理器
val dataManager = DeckDataManager(context)

// 卡組操作
val decks = dataManager.getAllDecks()
dataManager.createDeck(deck)
dataManager.updateDeck(deck)
dataManager.deleteDeck(deckId)

// 卡片操作
val cards = dataManager.getCardsInDeck(deckId)
dataManager.createCard(card)
dataManager.updateCard(card)
dataManager.deleteCard(cardId)
```

## 🎯 新功能開發流程

### 1. 添加新的UI組件

**步驟**:
1. 在`app/src/main/res/layout/`創建佈局文件
2. 在對應Activity中綁定佈局
3. 實現業務邏輯
4. 添加到導航流程

**範例**:
```kotlin
// 1. 創建Activity
class NewFeatureActivity : AppCompatActivity() {
    private lateinit var binding: ActivityNewFeatureBinding
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityNewFeatureBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupViews()
    }
}

// 2. 註冊到AndroidManifest.xml
<activity
    android:name=".ui.NewFeatureActivity"
    android:exported="false" />
```

### 2. 擴展圖片功能

**添加新的圖片操作**:
```kotlin
// 在RichTextEditText中添加新方法
fun rotateImage(imageSpan: ZoomableImageSpan, degrees: Float) {
    val matrix = Matrix()
    matrix.postRotate(degrees)
    
    val originalBitmap = (imageSpan.drawable as BitmapDrawable).bitmap
    val rotatedBitmap = Bitmap.createBitmap(
        originalBitmap, 0, 0,
        originalBitmap.width, originalBitmap.height,
        matrix, true
    )
    
    val newDrawable = BitmapDrawable(resources, rotatedBitmap)
    newDrawable.setBounds(imageSpan.drawable.bounds)
    
    // 更新ImageSpan
    // 注意：需要重新創建Span或使用反射修改
    invalidate()
}
```

### 3. 添加新的數據字段

**步驟**:
1. 修改數據實體類
2. 更新數據庫版本
3. 添加遷移邏輯
4. 更新DAO方法
5. 修改UI顯示

**範例**:
```kotlin
// 1. 修改Card實體
@Entity(tableName = "cards")
data class Card(
    // 現有字段...
    val newField: String = "",  // 新增字段
    // ...
)

// 2. 更新數據庫版本
@Database(
    entities = [Deck::class, Card::class],
    version = 2,  // 增加版本號
    exportSchema = false
)

// 3. 添加遷移
val MIGRATION_1_2 = object : Migration(1, 2) {
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("ALTER TABLE cards ADD COLUMN newField TEXT NOT NULL DEFAULT ''")
    }
}
```

## 🔍 調試和測試

### 1. 調試工具

**Toast調試**:
```kotlin
private fun showToast(message: String) {
    Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
}

// 使用
showToast("調試信息: $value")
```

**日誌調試**:
```kotlin
android.util.Log.d("TAG", "調試信息")
android.util.Log.e("TAG", "錯誤信息", exception)
```

**查看日誌**:
```bash
# 過濾特定標籤
adb logcat | grep "RichTextEditText"

# 清除日誌
adb logcat -c

# 保存日誌到文件
adb logcat > debug.log
```

### 2. 測試流程

**功能測試清單**:
- [ ] 卡組創建、編輯、刪除
- [ ] 卡片創建、編輯、刪除
- [ ] 圖片插入（拍照/相冊）
- [ ] 圖片縮放（雙指/雙擊）
- [ ] 卡片檢視（點擊切換）
- [ ] 數據持久化
- [ ] 權限處理

**圖片縮放測試**:
```kotlin
// 測試用例
fun testImageZoom() {
    // 1. 插入圖片
    val bitmap = createTestBitmap()
    richTextEditText.insertImage(bitmap)
    
    // 2. 模擬雙指縮放
    val event = createMultiTouchEvent()
    richTextEditText.onTouchEvent(event)
    
    // 3. 驗證縮放結果
    val imageSpan = getFirstImageSpan()
    val scale = getCurrentScale(imageSpan)
    assert(scale != 1.0f)
}
```

### 3. 性能測試

**記憶體使用**:
```kotlin
// 監控記憶體使用
val runtime = Runtime.getRuntime()
val usedMemory = runtime.totalMemory() - runtime.freeMemory()
Log.d("Memory", "Used: ${usedMemory / 1024 / 1024} MB")
```

**圖片載入性能**:
```kotlin
// 測量圖片載入時間
val startTime = System.currentTimeMillis()
val bitmap = imageManager.loadImage(filename)
val loadTime = System.currentTimeMillis() - startTime
Log.d("Performance", "Image load time: ${loadTime}ms")
```

## 🚨 常見問題解決

### 1. 編譯問題

**Gradle同步失敗**:
```bash
# 清理項目
./gradlew clean

# 重新同步
./gradlew sync

# 檢查網絡連接和代理設置
```

**依賴衝突**:
```gradle
// 排除衝突的依賴
implementation('com.example:library:1.0') {
    exclude group: 'com.conflict', module: 'module'
}
```

### 2. 運行時問題

**圖片載入失敗**:
```kotlin
// 檢查文件是否存在
val file = File(context.filesDir, "images/$filename")
if (!file.exists()) {
    Log.e("Image", "File not found: $filename")
    return null
}

// 檢查權限
if (ContextCompat.checkSelfPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE) 
    != PackageManager.PERMISSION_GRANTED) {
    // 請求權限
}
```

**數據庫錯誤**:
```kotlin
// 檢查數據庫版本
val db = Room.databaseBuilder(context, AppDatabase::class.java, "database")
    .fallbackToDestructiveMigration()  // 開發階段可用
    .build()
```

### 3. UI問題

**圖片縮放不工作**:
```kotlin
// 檢查多指觸控檢測
if (event.pointerCount >= 2) {
    Log.d("Touch", "Multi-touch detected: ${event.pointerCount}")
}

// 檢查圖片檢測
val imageSpan = getImageSpanAtPosition(x, y)
if (imageSpan != null) {
    Log.d("Image", "Image found at position")
}
```

**佈局問題**:
```xml
<!-- 確保RichTextEditText可以接收觸控事件 -->
<com.erroranalysis.app.ui.widgets.RichTextEditText
    android:focusable="true"
    android:clickable="true"
    android:layout_width="match_parent"
    android:layout_height="wrap_content" />
```

## 📈 性能優化建議

### 1. 圖片優化
- 壓縮圖片質量到80%
- 限制圖片最大尺寸
- 使用適當的圖片格式

### 2. 記憶體優化
- 及時回收Bitmap
- 使用WeakReference
- 避免記憶體洩漏

### 3. 數據庫優化
- 使用索引
- 批量操作
- 異步處理

### 4. UI優化
- 減少重繪次數
- 使用ViewHolder模式
- 延遲載入

## 🔧 代碼規範

### 1. 命名規範
```kotlin
// 類名：PascalCase
class CardViewActivity

// 方法名：camelCase
fun handleImageZoom()

// 變量名：camelCase
val currentScale = 1.0f

// 常量：UPPER_SNAKE_CASE
const val MAX_SCALE = 3.0f
```

### 2. 註釋規範
```kotlin
/**
 * 處理圖片縮放功能
 * @param event 觸控事件
 * @param actionMasked 動作類型
 */
private fun handleSimpleScale(event: MotionEvent, actionMasked: Int) {
    // 實現邏輯
}
```

### 3. 錯誤處理
```kotlin
try {
    // 可能出錯的操作
    val result = riskyOperation()
} catch (e: Exception) {
    Log.e("TAG", "操作失敗", e)
    // 降級處理
    fallbackOperation()
}
```
