[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: x86_64", "file_": "C:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\sdk\\libcxx_helper\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'C:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\sdk\\.cxx\\Debug\\5r5e101e\\x86_64\\android_gradle_build.json' was up-to-date", "file_": "C:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\sdk\\libcxx_helper\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "C:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\sdk\\libcxx_helper\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]