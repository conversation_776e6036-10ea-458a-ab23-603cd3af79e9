// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemQuickTemplateBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final MaterialCardView cardContainer;

  @NonNull
  public final MaterialCardView cardTemplatePreview;

  @NonNull
  public final TextView textTemplateDescription;

  @NonNull
  public final TextView textTemplateIcon;

  @NonNull
  public final TextView textTemplateName;

  @NonNull
  public final TextView textTemplateSubject;

  @NonNull
  public final TextView textTemplateTags;

  private ItemQuickTemplateBinding(@NonNull MaterialCardView rootView,
      @NonNull MaterialCardView cardContainer, @NonNull MaterialCardView cardTemplatePreview,
      @NonNull TextView textTemplateDescription, @NonNull TextView textTemplateIcon,
      @NonNull TextView textTemplateName, @NonNull TextView textTemplateSubject,
      @NonNull TextView textTemplateTags) {
    this.rootView = rootView;
    this.cardContainer = cardContainer;
    this.cardTemplatePreview = cardTemplatePreview;
    this.textTemplateDescription = textTemplateDescription;
    this.textTemplateIcon = textTemplateIcon;
    this.textTemplateName = textTemplateName;
    this.textTemplateSubject = textTemplateSubject;
    this.textTemplateTags = textTemplateTags;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemQuickTemplateBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemQuickTemplateBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_quick_template, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemQuickTemplateBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      MaterialCardView cardContainer = (MaterialCardView) rootView;

      id = R.id.card_template_preview;
      MaterialCardView cardTemplatePreview = ViewBindings.findChildViewById(rootView, id);
      if (cardTemplatePreview == null) {
        break missingId;
      }

      id = R.id.text_template_description;
      TextView textTemplateDescription = ViewBindings.findChildViewById(rootView, id);
      if (textTemplateDescription == null) {
        break missingId;
      }

      id = R.id.text_template_icon;
      TextView textTemplateIcon = ViewBindings.findChildViewById(rootView, id);
      if (textTemplateIcon == null) {
        break missingId;
      }

      id = R.id.text_template_name;
      TextView textTemplateName = ViewBindings.findChildViewById(rootView, id);
      if (textTemplateName == null) {
        break missingId;
      }

      id = R.id.text_template_subject;
      TextView textTemplateSubject = ViewBindings.findChildViewById(rootView, id);
      if (textTemplateSubject == null) {
        break missingId;
      }

      id = R.id.text_template_tags;
      TextView textTemplateTags = ViewBindings.findChildViewById(rootView, id);
      if (textTemplateTags == null) {
        break missingId;
      }

      return new ItemQuickTemplateBinding((MaterialCardView) rootView, cardContainer,
          cardTemplatePreview, textTemplateDescription, textTemplateIcon, textTemplateName,
          textTemplateSubject, textTemplateTags);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
