package com.erroranalysis.app.ui.base

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.erroranalysis.app.ui.theme.ThemeManager
import com.erroranalysis.app.ui.theme.AppTheme

/**
 * 支援主題的基礎Activity
 * 所有需要應用主題的Activity都應該繼承此類
 */
abstract class ThemedActivity : AppCompatActivity(), ThemeManager.ThemeChangeListener {
    
    protected lateinit var themeManager: ThemeManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        themeManager = ThemeManager.getInstance(this)
        // 不在這裡調用applyTheme()，讓子類在setContentView後調用
    }
    
    override fun onResume() {
        super.onResume()
        themeManager.addThemeChangeListener(this)
    }
    
    override fun onPause() {
        super.onPause()
        themeManager.removeThemeChangeListener(this)
    }
    
    override fun onThemeChanged(theme: AppTheme) {
        // 主題變更時重新應用主題
        applyTheme()
    }
    
    /**
     * 應用當前主題
     * 子類可以重寫此方法來自定義主題應用邏輯
     */
    protected open fun applyTheme() {
        val currentTheme = themeManager.getCurrentTheme()
        
        // 設置狀態欄顏色
        window.statusBarColor = currentTheme.getPrimaryDarkColorInt()
        
        // 子類可以重寫此方法來應用更多主題設置
        onApplyTheme(currentTheme)
    }
    
    /**
     * 子類重寫此方法來應用特定的主題設置
     */
    protected abstract fun onApplyTheme(theme: AppTheme)
}
