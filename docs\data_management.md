# 數據管理系統

## 🗄️ **數據存儲架構**

### **存儲層次結構**

```
Application Data
├── SharedPreferences          # 應用設置和卡組索引
│   ├── app_preferences        # 應用偏好設置
│   ├── deck_list             # 卡組列表索引
│   └── user_settings         # 用戶設置
├── Internal Storage          # 內部文件存儲
│   ├── decks/                # 卡組數據文件
│   │   ├── deck_001.json     # 卡組詳細數據
│   │   ├── deck_002.json
│   │   └── ...
│   └── images/               # 圖像文件
│       ├── img_001.jpg       # 題目和答案圖片
│       ├── img_002.jpg
│       └── ...
└── Cache                     # 臨時緩存
    ├── temp_images/          # 臨時圖像
    └── processing/           # 處理中的文件
```

### **數據持久化策略**

**SharedPreferences 用途**：
- 應用程式設置
- 卡組列表和元數據
- 用戶偏好設置
- 學習統計數據

**JSON 文件用途**：
- 卡組詳細數據
- 卡片完整內容
- 匯入/匯出數據
- 備份和恢復

**圖像文件用途**：
- 題目圖片
- 答案圖片
- 拍照結果
- 處理後的圖像

## 📊 **數據模型設計**

### **核心數據模型**

**StudyDeck (卡組模型)**：
```kotlin
data class StudyDeck(
    val id: String,                    // 唯一識別碼
    val name: String,                  // 卡組名稱
    val description: String = "",      // 卡組描述
    val icon: String = "📚",          // 卡組圖標
    val color: String = "#6B7280",     // 卡組顏色
    val createdAt: Long,               // 創建時間
    val updatedAt: Long,               // 更新時間
    val cardCount: Int = 0,            // 卡片數量
    val studiedCount: Int = 0,         // 已學習數量
    val tags: List<String> = emptyList(), // 標籤
    val settings: DeckSettings = DeckSettings() // 卡組設置
)

data class DeckSettings(
    val studyMode: StudyMode = StudyMode.FLASHCARD,
    val shuffleCards: Boolean = true,
    val showHints: Boolean = true,
    val autoAdvance: Boolean = false
)

enum class StudyMode {
    FLASHCARD,    // 閃卡模式
    QUIZ,         // 測驗模式
    REVIEW        // 複習模式
}
```

**StudyCard (卡片模型)**：
```kotlin
data class StudyCard(
    val id: String,                    // 唯一識別碼
    val deckId: String,                // 所屬卡組ID
    val question: String,              // 題目內容（JSON格式）
    val answer: String,                // 答案內容（JSON格式）
    val tags: List<String> = emptyList(), // 標籤
    val difficulty: CardDifficulty = CardDifficulty.NORMAL, // 難度
    val createdAt: Long,               // 創建時間
    val updatedAt: Long,               // 更新時間
    val studiedCount: Int = 0,         // 學習次數
    val correctCount: Int = 0,         // 正確次數
    val lastStudiedAt: Long? = null,   // 最後學習時間
    val nextReviewAt: Long? = null,    // 下次複習時間
    val interval: Int = 1,             // 複習間隔（天）
    val easeFactor: Float = 2.5f,      // 難易度因子
    val metadata: CardMetadata = CardMetadata() // 元數據
)

data class CardMetadata(
    val source: String = "manual",     // 來源：manual, import, camera
    val originalImagePath: String? = null, // 原始圖片路徑
    val processingHistory: List<String> = emptyList(), // 處理歷史
    val aiGenerated: Boolean = false   // 是否AI生成
)

enum class CardDifficulty(val displayName: String, val color: String) {
    EASY("簡單", "#4CAF50"),
    NORMAL("普通", "#2196F3"),
    HARD("困難", "#FF9800"),
    VERY_HARD("非常困難", "#F44336")
}
```

### **圖文混合內容格式**

**內容結構**：
```json
[
  {
    "type": "text",
    "content": "解下列方程式："
  },
  {
    "type": "image",
    "content": "equation_001.jpg"
  },
  {
    "type": "text",
    "content": "其中 x 為未知數"
  }
]
```

**支援的內容類型**：
- `text`: 純文字內容
- `image`: 圖片文件名
- `formula`: 數學公式（LaTeX格式）
- `code`: 程式碼片段

## 🔧 **數據管理器實現**

### **DeckDataManager (卡組數據管理器)**

```kotlin
class DeckDataManager private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: DeckDataManager? = null
        
        fun getInstance(context: Context): DeckDataManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DeckDataManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val prefs = context.getSharedPreferences("deck_data", Context.MODE_PRIVATE)
    private val gson = Gson()
    
    // 卡組操作
    fun getAllDecks(): List<StudyDeck> {
        val deckIds = getDeckIds()
        return deckIds.mapNotNull { getDeck(it) }
    }
    
    fun getDeck(id: String): StudyDeck? {
        val deckFile = File(context.filesDir, "decks/$id.json")
        return if (deckFile.exists()) {
            try {
                val json = deckFile.readText()
                gson.fromJson(json, StudyDeck::class.java)
            } catch (e: Exception) {
                Log.e("DeckDataManager", "Error reading deck $id", e)
                null
            }
        } else null
    }
    
    fun saveDeck(deck: StudyDeck): Boolean {
        return try {
            // 保存卡組詳細數據
            val deckFile = File(context.filesDir, "decks/${deck.id}.json")
            deckFile.parentFile?.mkdirs()
            deckFile.writeText(gson.toJson(deck))
            
            // 更新卡組索引
            updateDeckIndex(deck)
            
            true
        } catch (e: Exception) {
            Log.e("DeckDataManager", "Error saving deck ${deck.id}", e)
            false
        }
    }
    
    fun deleteDeck(id: String): Boolean {
        return try {
            // 刪除卡組文件
            val deckFile = File(context.filesDir, "decks/$id.json")
            deckFile.delete()
            
            // 刪除相關卡片
            deleteAllCardsInDeck(id)
            
            // 更新索引
            removeDeckFromIndex(id)
            
            true
        } catch (e: Exception) {
            Log.e("DeckDataManager", "Error deleting deck $id", e)
            false
        }
    }
    
    // 卡片操作
    fun getCardsInDeck(deckId: String): List<StudyCard> {
        val deck = getDeck(deckId) ?: return emptyList()
        return deck.cards
    }
    
    fun saveCard(card: StudyCard): Boolean {
        val deck = getDeck(card.deckId) ?: return false
        val updatedCards = deck.cards.toMutableList()
        
        val existingIndex = updatedCards.indexOfFirst { it.id == card.id }
        if (existingIndex >= 0) {
            updatedCards[existingIndex] = card
        } else {
            updatedCards.add(card)
        }
        
        val updatedDeck = deck.copy(
            cards = updatedCards,
            cardCount = updatedCards.size,
            updatedAt = System.currentTimeMillis()
        )
        
        return saveDeck(updatedDeck)
    }
    
    fun deleteCard(cardId: String, deckId: String): Boolean {
        val deck = getDeck(deckId) ?: return false
        val updatedCards = deck.cards.filter { it.id != cardId }
        
        val updatedDeck = deck.copy(
            cards = updatedCards,
            cardCount = updatedCards.size,
            updatedAt = System.currentTimeMillis()
        )
        
        return saveDeck(updatedDeck)
    }
    
    // 統計和查詢
    fun getDeckStatistics(deckId: String): DeckStatistics? {
        val deck = getDeck(deckId) ?: return null
        val cards = deck.cards
        
        return DeckStatistics(
            totalCards = cards.size,
            studiedCards = cards.count { it.studiedCount > 0 },
            masteredCards = cards.count { it.correctCount >= 3 },
            averageAccuracy = if (cards.isNotEmpty()) {
                cards.map { 
                    if (it.studiedCount > 0) it.correctCount.toFloat() / it.studiedCount else 0f 
                }.average().toFloat()
            } else 0f,
            lastStudiedAt = cards.mapNotNull { it.lastStudiedAt }.maxOrNull()
        )
    }
    
    fun searchCards(query: String, deckId: String? = null): List<StudyCard> {
        val decks = if (deckId != null) {
            listOfNotNull(getDeck(deckId))
        } else {
            getAllDecks()
        }
        
        return decks.flatMap { deck ->
            deck.cards.filter { card ->
                card.question.contains(query, ignoreCase = true) ||
                card.answer.contains(query, ignoreCase = true) ||
                card.tags.any { it.contains(query, ignoreCase = true) }
            }
        }
    }
    
    // 私有輔助方法
    private fun getDeckIds(): List<String> {
        val deckIdsJson = prefs.getString("deck_ids", "[]") ?: "[]"
        return gson.fromJson(deckIdsJson, Array<String>::class.java).toList()
    }
    
    private fun updateDeckIndex(deck: StudyDeck) {
        val deckIds = getDeckIds().toMutableList()
        if (!deckIds.contains(deck.id)) {
            deckIds.add(deck.id)
            val deckIdsJson = gson.toJson(deckIds)
            prefs.edit().putString("deck_ids", deckIdsJson).apply()
        }
        
        // 保存卡組元數據到 SharedPreferences
        val deckMetadata = DeckMetadata(
            id = deck.id,
            name = deck.name,
            cardCount = deck.cardCount,
            updatedAt = deck.updatedAt
        )
        val metadataJson = gson.toJson(deckMetadata)
        prefs.edit().putString("deck_meta_${deck.id}", metadataJson).apply()
    }
    
    private fun removeDeckFromIndex(deckId: String) {
        val deckIds = getDeckIds().toMutableList()
        deckIds.remove(deckId)
        val deckIdsJson = gson.toJson(deckIds)
        prefs.edit()
            .putString("deck_ids", deckIdsJson)
            .remove("deck_meta_$deckId")
            .apply()
    }
    
    private fun deleteAllCardsInDeck(deckId: String) {
        // 刪除卡片相關的圖片文件
        val deck = getDeck(deckId)
        deck?.cards?.forEach { card ->
            deleteCardImages(card)
        }
    }
    
    private fun deleteCardImages(card: StudyCard) {
        // 解析圖文混合內容，刪除相關圖片
        try {
            val questionContent = gson.fromJson(card.question, Array<ContentItem>::class.java)
            val answerContent = gson.fromJson(card.answer, Array<ContentItem>::class.java)
            
            (questionContent + answerContent)
                .filter { it.type == "image" }
                .forEach { contentItem ->
                    val imageFile = File(context.filesDir, "images/${contentItem.content}")
                    imageFile.delete()
                }
        } catch (e: Exception) {
            Log.w("DeckDataManager", "Error deleting card images", e)
        }
    }
}

data class DeckMetadata(
    val id: String,
    val name: String,
    val cardCount: Int,
    val updatedAt: Long
)

data class DeckStatistics(
    val totalCards: Int,
    val studiedCards: Int,
    val masteredCards: Int,
    val averageAccuracy: Float,
    val lastStudiedAt: Long?
)

data class ContentItem(
    val type: String,
    val content: String
)
```

## 🔄 **數據同步和備份**

### **數據備份策略**

```kotlin
class BackupManager(private val context: Context) {
    
    fun createBackup(): File? {
        return try {
            val backupDir = File(context.getExternalFilesDir(null), "backups")
            backupDir.mkdirs()
            
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
                .format(Date())
            val backupFile = File(backupDir, "backup_$timestamp.zip")
            
            ZipOutputStream(FileOutputStream(backupFile)).use { zip ->
                // 備份卡組數據
                backupDecks(zip)
                // 備份圖片
                backupImages(zip)
                // 備份設置
                backupSettings(zip)
            }
            
            backupFile
        } catch (e: Exception) {
            Log.e("BackupManager", "Error creating backup", e)
            null
        }
    }
    
    fun restoreBackup(backupFile: File): Boolean {
        return try {
            ZipInputStream(FileInputStream(backupFile)).use { zip ->
                var entry = zip.nextEntry
                while (entry != null) {
                    when {
                        entry.name.startsWith("decks/") -> restoreDeck(zip, entry)
                        entry.name.startsWith("images/") -> restoreImage(zip, entry)
                        entry.name == "settings.json" -> restoreSettings(zip)
                    }
                    entry = zip.nextEntry
                }
            }
            true
        } catch (e: Exception) {
            Log.e("BackupManager", "Error restoring backup", e)
            false
        }
    }
}
```

### **數據遷移策略**

```kotlin
class DataMigration(private val context: Context) {
    
    fun migrateToVersion(targetVersion: Int) {
        val currentVersion = getCurrentDataVersion()
        
        for (version in (currentVersion + 1)..targetVersion) {
            when (version) {
                2 -> migrateToV2()
                3 -> migrateToV3()
                // 添加更多版本遷移
            }
        }
        
        setCurrentDataVersion(targetVersion)
    }
    
    private fun migrateToV2() {
        // 添加卡片元數據字段
        val deckManager = DeckDataManager.getInstance(context)
        val decks = deckManager.getAllDecks()
        
        decks.forEach { deck ->
            val updatedCards = deck.cards.map { card ->
                card.copy(metadata = CardMetadata())
            }
            val updatedDeck = deck.copy(cards = updatedCards)
            deckManager.saveDeck(updatedDeck)
        }
    }
    
    private fun migrateToV3() {
        // 添加學習統計功能
        // 遷移邏輯...
    }
}
```

這份數據管理文檔詳細說明了應用程式的數據存儲架構、模型設計和管理策略，為開發者提供了完整的數據層指導。
