# CSV匯入錯誤信息改進

## 問題描述

用戶反映CSV匯入檢查有兩個問題：
1. 為何會出現"國小數學.csv"這個檔案名稱
2. 缺少必要欄位的錯誤信息沒有標示是哪一列

## 改進內容

### 1. 詳細的欄位錯誤信息

**改進前**：
```
CSV缺少必要欄位：題目ID（或ID/id/題目id）, 答案（或answer/Answer）

實際欄位：題目,內容,解答,難度
```

**改進後**：
```
CSV檔案格式錯誤：缺少必要欄位

❌ 缺少的欄位：題目ID（或ID/id/題目id）, 答案（或answer/Answer）

📋 實際欄位（第1行）：
   第1列：題目
   第2列：內容  
   第3列：解答
   第4列：難度

💡 請確認CSV檔案第1行包含以下必要欄位：
   • 題目ID（或ID、id、題目id）
   • 答案（或answer、Answer）
```

### 2. 具體的行列錯誤信息

**改進前**：
```
第3行解析失敗：答案不能為空
第5行欄位數量不匹配
```

**改進後**：
```
第3行錯誤：答案欄位（第4列）不能為空
第5行欄位數量不匹配：期望8個欄位，實際6個欄位
第7行錯誤：題目ID欄位（第1列）格式錯誤
```

### 3. 檔案名稱追蹤改進

**新增詳細日誌**：
```kotlin
Log.d(TAG, "=== 開始CSV匯入 ===")
Log.d(TAG, "檔案名稱: $fileName")
Log.d(TAG, "匯入CSV檔案: ${csvFile.name}（完整路徑: ${csvFile.absolutePath}）")
Log.d(TAG, "CSV前100字符: ${csvContent.take(100)}")
```

**錯誤信息包含檔案名稱**：
```kotlin
return ImportResult.Error("CSV檔案「$fileName」為空或無法讀取")
return ImportResult.Error("CSV檔案「$fileName」標題行為空")
```

## 錯誤信息範例

### 1. 缺少必要欄位
```
CSV檔案格式錯誤：缺少必要欄位

❌ 缺少的欄位：答案（或answer/Answer）

📋 實際欄位（第1行）：
   第1列：題目ID
   第2列：題目文字
   第3列：題目圖片
   第4列：標籤
   第5列：難度
   第6列：說明

💡 請確認CSV檔案第1行包含以下必要欄位：
   • 題目ID（或ID、id、題目id）
   • 答案（或answer、Answer）
```

### 2. 欄位數量不匹配
```
第3行欄位數量不匹配：期望8個欄位，實際6個欄位
```

### 3. 答案欄位為空
```
第5行錯誤：答案欄位（第4列）不能為空
```

### 4. 題目ID格式錯誤
```
第2行錯誤：題目ID欄位（第1列）格式錯誤
```

### 5. 標題行格式錯誤
```
CSV檔案「數學-代數基礎.csv」標題行格式錯誤：Unexpected character at position 5

標題行內容：題目ID,題目文字"答案,標籤
```

## 檔案名稱問題排查

### 可能的原因

1. **用戶選擇了錯誤的檔案**
   - 實際選擇的檔案確實是"國小數學.csv"
   - 檔案選擇器顯示的名稱與實際檔案名稱不同

2. **檔案名稱編碼問題**
   - 檔案名稱包含特殊字符
   - 系統編碼轉換問題

3. **檔案來源問題**
   - 從其他應用程式分享的檔案
   - 雲端下載的檔案名稱變更

### 排查方法

**新增的詳細日誌**：
```kotlin
Log.d(TAG, "=== 開始CSV匯入 ===")
Log.d(TAG, "檔案名稱: $fileName")
Log.d(TAG, "匯入CSV檔案: ${csvFile.name}（完整路徑: ${csvFile.absolutePath}）")
```

**檢查步驟**：
1. 查看logcat中的檔案名稱日誌
2. 確認用戶實際選擇的檔案
3. 檢查檔案路徑和名稱編碼

## 使用指南

### 1. 檢查錯誤信息
當匯入失敗時，錯誤信息會顯示：
- 具體的檔案名稱
- 缺少的欄位名稱和位置
- 錯誤的行號和列號
- 具體的錯誤原因

### 2. 修復CSV檔案
根據錯誤信息：
- 添加缺少的必要欄位
- 修正欄位數量不匹配的行
- 填入空白的必要欄位
- 修正格式錯誤的內容

### 3. 驗證修復
- 重新匯入修正後的檔案
- 檢查是否還有其他錯誤
- 確認所有題目正確匯入

## 技術實現

### 1. 欄位映射檢查
```kotlin
val requiredFields = mapOf(
    "id" to listOf("題目ID", "ID", "id", "題目id"),
    "answer" to listOf("答案", "answer", "Answer")
)

val missingFields = mutableListOf<String>()
requiredFields.forEach { (fieldType, variants) ->
    val found = variants.any { variant ->
        headers.any { it.equals(variant, ignoreCase = true) }
    }
    if (!found) {
        missingFields.add("${variants[0]}（或${variants.joinToString("/")}）")
    }
}
```

### 2. 詳細錯誤信息構建
```kotlin
val errorMessage = buildString {
    append("CSV檔案格式錯誤：缺少必要欄位\n\n")
    append("❌ 缺少的欄位：${missingFields.joinToString(", ")}\n\n")
    append("📋 實際欄位（第1行）：\n")
    headers.forEachIndexed { index, header ->
        append("   第${index + 1}列：$header\n")
    }
    append("\n💡 請確認CSV檔案第1行包含以下必要欄位：\n")
    append("   • 題目ID（或ID、id、題目id）\n")
    append("   • 答案（或answer、Answer）")
}
```

### 3. 行列錯誤定位
```kotlin
val errorMsg = when {
    e.message?.contains("答案不能為空") == true -> 
        "第${i+1}行錯誤：答案欄位（第${fieldMap["answer"]?.plus(1) ?: "?"}列）不能為空"
    e.message?.contains("題目ID") == true -> 
        "第${i+1}行錯誤：題目ID欄位（第${fieldMap["id"]?.plus(1) ?: "?"}列）格式錯誤"
    else -> 
        "第${i+1}行解析失敗：${e.message}"
}
```

## 預期效果

### 1. 更清晰的錯誤定位
- 用戶能快速找到問題所在的行和列
- 明確知道需要修正的內容
- 減少反覆試錯的時間

### 2. 更好的用戶體驗
- 友好的錯誤信息格式
- 具體的修復建議
- 詳細的檔案信息追蹤

### 3. 更高效的問題排查
- 開發者能快速定位問題
- 詳細的日誌信息
- 完整的錯誤上下文

這些改進應該能顯著提升CSV匯入功能的可用性和錯誤診斷能力！
