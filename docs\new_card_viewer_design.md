# 全新卡片檢視功能設計

## 設計理念

完全重新設計卡片檢視功能，擺脫舊框架的限制，創建一個更簡潔、更可靠、更易維護的解決方案。

## 核心優勢

### 1. 簡潔的架構
- **單一職責**：專注於卡片內容的顯示和切換
- **最小依賴**：只依賴必要的組件
- **清晰邏輯**：直觀的代碼結構

### 2. 可靠的實現
- **原生組件**：使用Android原生的TextView和ScrollView
- **錯誤處理**：完整的異常捕獲和降級處理
- **穩定性**：避免複雜的自定義組件

### 3. 優秀的用戶體驗
- **直觀操作**：點擊任意位置切換內容
- **流暢滾動**：原生ScrollView的流暢體驗
- **清晰指示**：明確的狀態提示

## 技術架構

### 1. 佈局設計
```xml
LinearLayout (根容器)
├── AppBarLayout (工具列)
├── LinearLayout (狀態指示區)
│   ├── TextView (內容類型)
│   └── TextView (操作提示)
├── FrameLayout (主要內容區)
│   ├── ScrollView (可滾動容器)
│   │   └── TextView (內容顯示)
│   └── ProgressBar (載入指示器)
└── LinearLayout (底部操作區)
    ├── Button (編輯)
    └── Button (切換)
```

### 2. 核心組件

#### CardViewerActivity
- **職責**：管理卡片檢視的整個生命週期
- **特點**：簡潔的邏輯，完整的錯誤處理
- **優勢**：易於維護和擴展

#### 原生TextView
- **替代**：複雜的RichTextEditText
- **優勢**：穩定、可靠、性能好
- **功能**：支援文字選擇、滾動、格式化

#### 原生ScrollView
- **替代**：EditText的內部滾動
- **優勢**：流暢的觸控體驗
- **功能**：慣性滾動、邊界回彈

### 3. 數據處理

#### JSON內容解析
```kotlin
private fun extractTextContent(content: String): String {
    return try {
        if (content.trim().startsWith("[")) {
            // JSON格式處理
            val jsonArray = org.json.JSONArray(content)
            val textBuilder = StringBuilder()
            
            for (i in 0 until jsonArray.length()) {
                val item = jsonArray.getJSONObject(i)
                when (item.getString("type")) {
                    "text" -> textBuilder.append(item.getString("content"))
                    "image" -> textBuilder.append("[圖片: ${item.getString("content")}]")
                }
            }
            textBuilder.toString()
        } else {
            // 純文字處理
            content
        }
    } catch (e: Exception) {
        // 降級處理
        content
    }
}
```

#### 內容組合
```kotlin
private fun displayAnswerContent() {
    val answerContent = extractTextContent(card.answer)
    val aiAnswerContent = if (card.aiAnswer.isNotEmpty()) {
        extractTextContent(card.aiAnswer)
    } else ""

    val combinedContent = buildString {
        append("📝 答案\n")
        append(answerContent)
        
        if (aiAnswerContent.isNotEmpty()) {
            append("\n\n🤖 AI解答\n")
            append(aiAnswerContent)
        }
    }

    binding.tvContent.text = combinedContent
}
```

## 功能特點

### 1. 內容顯示
- **題目模式**：顯示卡片題目
- **答案模式**：同時顯示答案和AI解答
- **自動格式化**：智能處理JSON和純文字格式

### 2. 交互方式
- **點擊切換**：點擊內容區域或按鈕切換
- **滾動查看**：支援長內容的流暢滾動
- **編輯功能**：快速跳轉到編輯模式

### 3. 狀態管理
- **清晰指示**：顯示當前內容類型
- **操作提示**：提示下一步操作
- **自動重置**：切換時自動滾動到頂部

### 4. 錯誤處理
- **分層處理**：多層次的錯誤恢復
- **用戶友好**：清晰的錯誤信息
- **穩定運行**：錯誤不影響基本功能

## 用戶體驗改進

### 1. 視覺設計
- **現代化界面**：Material Design風格
- **清晰層次**：合理的信息架構
- **舒適閱讀**：適當的字體和間距

### 2. 操作體驗
- **直觀操作**：符合用戶習慣的交互
- **即時反饋**：操作結果立即可見
- **流暢動畫**：平滑的過渡效果

### 3. 內容呈現
- **完整顯示**：支援任意長度的內容
- **格式保持**：保留重要的格式信息
- **智能處理**：自動處理不同格式的內容

## 技術優勢

### 1. 性能優化
- **輕量級**：最小的資源佔用
- **快速載入**：簡化的初始化流程
- **流暢滾動**：原生組件的優秀性能

### 2. 維護性
- **清晰結構**：易於理解的代碼組織
- **模塊化**：功能明確分離
- **可擴展**：便於添加新功能

### 3. 穩定性
- **原生組件**：避免自定義組件的問題
- **錯誤恢復**：完整的異常處理
- **向下兼容**：支援各種數據格式

## 與舊版本對比

| 特性 | 舊版本 | 新版本 |
|------|--------|--------|
| **核心組件** | RichTextEditText | TextView + ScrollView |
| **佈局複雜度** | 複雜的嵌套結構 | 簡潔的線性結構 |
| **滾動方式** | EditText內部滾動 | ScrollView外層滾動 |
| **錯誤處理** | 部分處理 | 完整的多層處理 |
| **維護難度** | 高 | 低 |
| **穩定性** | 一般 | 優秀 |
| **性能** | 一般 | 優秀 |
| **用戶體驗** | 複雜 | 簡潔直觀 |

## 使用指南

### 1. 基本操作
- **查看題目**：進入卡片後默認顯示題目
- **查看答案**：點擊內容區域或"查看答案"按鈕
- **返回題目**：再次點擊內容區域或"查看題目"按鈕
- **編輯卡片**：點擊"編輯卡片"按鈕

### 2. 滾動操作
- **長內容滾動**：在內容區域上下滑動
- **自動重置**：切換內容時自動回到頂部
- **流暢體驗**：支援慣性滾動和邊界回彈

### 3. 內容格式
- **純文字**：直接顯示文字內容
- **JSON格式**：自動解析並顯示
- **圖片引用**：顯示為"[圖片: 檔名]"格式

## 未來擴展

### 1. 功能增強
- **圖片顯示**：支援內嵌圖片顯示
- **數學公式**：支援LaTeX公式渲染
- **語音朗讀**：添加文字轉語音功能

### 2. 交互改進
- **手勢支援**：支援更多手勢操作
- **快捷鍵**：支援鍵盤快捷鍵
- **個性化**：支援字體大小調整

### 3. 智能功能
- **學習進度**：記錄學習狀態
- **智能推薦**：推薦相關卡片
- **統計分析**：學習數據分析

這個全新的設計徹底解決了舊版本的問題，提供了更穩定、更流暢的卡片檢視體驗！
