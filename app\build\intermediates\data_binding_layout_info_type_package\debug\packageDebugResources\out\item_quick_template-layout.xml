<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_quick_template" modulePackage="com.erroranalysis.app" filePath="app\src\main\res\layout\item_quick_template.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView" rootNodeViewId="@+id/card_container"><Targets><Target id="@+id/card_container" tag="layout/item_quick_template_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="92" endOffset="51"/></Target><Target id="@+id/card_template_preview" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="18" startOffset="8" endLine="42" endOffset="59"/></Target><Target id="@+id/text_template_icon" view="TextView"><Expressions/><location startLine="32" startOffset="16" endLine="38" endOffset="45"/></Target><Target id="@+id/text_template_name" view="TextView"><Expressions/><location startLine="50" startOffset="12" endLine="56" endOffset="42"/></Target><Target id="@+id/text_template_subject" view="TextView"><Expressions/><location startLine="58" startOffset="12" endLine="68" endOffset="41"/></Target><Target id="@+id/text_template_tags" view="TextView"><Expressions/><location startLine="70" startOffset="12" endLine="76" endOffset="41"/></Target><Target id="@+id/text_template_description" view="TextView"><Expressions/><location startLine="78" startOffset="12" endLine="86" endOffset="41"/></Target></Targets></Layout>