# 直接CSV匯入測試指南

## 🎯 重大改進

我們已經完全重寫了CSV匯入邏輯，**完全跳過JSON中介格式**：

**舊流程**：CSV → JSON → 卡片
**新流程**：CSV → 卡片（直接）

這應該解決圖片匯入的問題！

## 📱 測試步驟

### 步驟1：準備測試檔案

**重要**：使用提供的UTF-8編碼檔案 `docs/test_direct.csv`

或者手動創建，**務必保存為UTF-8編碼**：

**test_direct.csv**：
```csv
題目ID,題目文字,題目圖片,答案
Q001,有文字的題目,test1.jpg,答案1
Q002,,test2.jpg,答案2
Q003,純文字題目,,答案3
```

**編碼注意事項**：
- 在記事本中：另存新檔 → 編碼選擇「UTF-8」
- 在Excel中：另存新檔 → 檔案類型選擇「CSV UTF-8 (逗號分隔)」
- 在VS Code中：右下角顯示「UTF-8」編碼

### 步驟2：創建ZIP檔案

```
test_direct.zip
├── test_direct.csv
└── images/
    ├── test1.jpg  (任何JPG圖片)
    └── test2.jpg  (任何JPG圖片)
```

### 步驟3：執行匯入並收集日誌

```bash
# 清除舊日誌
adb logcat -c

# 開始收集日誌
adb logcat | grep "BatchImportManager" > direct_test.log &

# 執行匯入操作
# (在手機上選擇test_direct.zip並匯入)

# 停止日誌收集 (Ctrl+C)
```

### 步驟4：檢查關鍵日誌

#### 4.1 確認使用直接匯入
查找：
```
=== 完全直接CSV匯入開始 ===
檔案名稱: test_direct.csv
資料行數: 3
圖片目錄: /path/to/temp/images
```

#### 4.2 檢查每行處理
查找：
```
=== 處理第1行 ===
CSV行值: [Q001, 有文字的題目, test1.jpg, 答案1]
=== 直接從CSV行創建卡片 ===
提取的值:
  questionText: '有文字的題目'
  questionImage: 'test1.jpg'
  answer: '答案1'
  answerImage: ''
✅ 文字+圖片題目
✅ 第1行匯入成功

=== 處理第2行 ===
CSV行值: [Q002, , test2.jpg, 答案2]
=== 直接從CSV行創建卡片 ===
提取的值:
  questionText: ''
  questionImage: 'test2.jpg'
  answer: '答案2'
  answerImage: ''
✅ 純圖片題目: test2.jpg
✅ 第2行匯入成功

=== 處理第3行 ===
CSV行值: [Q003, 純文字題目, , 答案3]
=== 直接從CSV行創建卡片 ===
提取的值:
  questionText: '純文字題目'
  questionImage: ''
  answer: '答案3'
  answerImage: ''
✅ 純文字題目
✅ 第3行匯入成功
```

#### 4.3 檢查圖片處理
查找：
```
=== 直接構建內容 ===
text: '有文字的題目'
imageName: 'test1.jpg'
開始處理圖片：test1.jpg
圖片路徑：/path/to/temp/images/test1.jpg
圖片存在：true
✅ 圖片處理成功：uuid-filename.jpg
✅ 內容構建完成：[{"type":"text","content":"有文字的題目"},{"type":"image","content":"uuid-filename.jpg"}]

=== 直接構建內容 ===
text: ''
imageName: 'test2.jpg'
開始處理圖片：test2.jpg
圖片路徑：/path/to/temp/images/test2.jpg
圖片存在：true
✅ 圖片處理成功：uuid-filename.jpg
✅ 內容構建完成：[{"type":"image","content":"uuid-filename.jpg"}]
```

#### 4.4 檢查最終結果
查找：
```
=== 完全直接CSV匯入完成 ===
成功: 3, 失敗: 0
```

### 步驟5：檢查應用程式結果

1. **進入新創建的卡組**
2. **檢查卡片數量**：應該有3張卡片
3. **檢查每張卡片**：
   - **Q001**：應該有文字"有文字的題目" + 圖片
   - **Q002**：應該只有圖片（這是關鍵測試！）
   - **Q003**：應該只有文字"純文字題目"

## 🎯 預期結果

### 如果修復成功

1. **日誌中應該看到**：
   - `=== 完全直接CSV匯入開始 ===`
   - `✅ 純圖片題目: test2.jpg`
   - `✅ 圖片處理成功`
   - `成功: 3, 失敗: 0`

2. **應用程式中應該看到**：
   - 卡組包含3張卡片
   - Q002卡片只有圖片，沒有文字
   - 圖片在卡片中正確顯示

### 如果仍有問題

1. **檢查日誌中的錯誤信息**
2. **確認圖片處理步驟**
3. **檢查內容構建過程**

## 🔍 關鍵改進

### 1. 完全跳過JSON
- 不再將CSV轉換為JSON
- 直接從CSV行創建卡片
- 避免JSON轉換過程中的數據丟失

### 2. 直接圖片處理
- 在卡片創建時直接處理圖片
- 不依賴JSON中的圖片信息
- 確保圖片處理邏輯正確執行

### 3. 詳細的診斷日誌
- 每個步驟都有詳細記錄
- 可以清楚看到問題出現在哪裡
- 幫助快速定位和解決問題

## 📋 測試檢查清單

### 基本功能
- [ ] 使用直接匯入流程（不是JSON流程）
- [ ] 3張卡片都成功匯入
- [ ] 沒有匯入錯誤

### 圖片功能
- [ ] Q001的圖片正確顯示
- [ ] Q002（只有圖片）成功匯入並顯示
- [ ] 圖片在RichTextEditor中正確載入

### 日誌檢查
- [ ] 看到"完全直接CSV匯入"日誌
- [ ] 看到"純圖片題目"識別
- [ ] 看到圖片處理成功
- [ ] 看到內容構建完成

## 🎉 成功標準

如果這次修復成功，您應該看到：

1. **Q002（只有圖片的題目）成功匯入**
2. **圖片在卡片中正確顯示**
3. **沒有任何錯誤信息**
4. **日誌顯示直接匯入流程**

這個完全重寫的版本應該解決所有圖片匯入問題！

## 📞 回報結果

請測試後告訴我：

1. **Q002是否成功匯入？**
2. **圖片是否在卡片中顯示？**
3. **日誌是否顯示直接匯入流程？**
4. **是否還有任何錯誤？**

如果問題仍然存在，請提供完整的`direct_test.log`檔案。
