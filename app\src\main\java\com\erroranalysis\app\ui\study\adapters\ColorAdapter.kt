package com.erroranalysis.app.ui.study.adapters

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.erroranalysis.app.databinding.ItemColorBinding

/**
 * 顏色選擇適配器
 */
class ColorAdapter(
    private val onColorSelected: (String) -> Unit
) : ListAdapter<String, ColorAdapter.ColorViewHolder>(ColorDiffCallback()) {
    
    private var selectedColor: String? = null
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ColorViewHolder {
        val binding = ItemColorBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return ColorViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: ColorViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    fun setSelectedColor(color: String) {
        val oldSelected = selectedColor
        selectedColor = color
        
        // 更新舊選中項目
        oldSelected?.let { old ->
            val oldIndex = currentList.indexOf(old)
            if (oldIndex != -1) {
                notifyItemChanged(oldIndex)
            }
        }
        
        // 更新新選中項目
        val newIndex = currentList.indexOf(color)
        if (newIndex != -1) {
            notifyItemChanged(newIndex)
        }
    }
    
    inner class ColorViewHolder(
        private val binding: ItemColorBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(colorHex: String) {
            try {
                val color = Color.parseColor(colorHex)
                
                // 設置顏色圓圈
                val drawable = GradientDrawable().apply {
                    shape = GradientDrawable.OVAL
                    setColor(color)
                }
                binding.viewColor.background = drawable
                
                // 設置選中狀態
                val isSelected = colorHex == selectedColor
                if (isSelected) {
                    binding.viewColorBorder.visibility = android.view.View.VISIBLE
                    val borderDrawable = GradientDrawable().apply {
                        shape = GradientDrawable.OVAL
                        setStroke(6, Color.parseColor("#2196F3"))
                        setColor(Color.TRANSPARENT)
                    }
                    binding.viewColorBorder.background = borderDrawable
                } else {
                    binding.viewColorBorder.visibility = android.view.View.GONE
                }
                
                // 設置點擊事件
                binding.root.setOnClickListener {
                    setSelectedColor(colorHex)
                    onColorSelected(colorHex)
                }
                
            } catch (e: Exception) {
                // 處理無效顏色
                binding.viewColor.setBackgroundColor(Color.GRAY)
            }
        }
    }
    
    private class ColorDiffCallback : DiffUtil.ItemCallback<String>() {
        override fun areItemsTheSame(oldItem: String, newItem: String): Boolean {
            return oldItem == newItem
        }
        
        override fun areContentsTheSame(oldItem: String, newItem: String): Boolean {
            return oldItem == newItem
        }
    }
}
