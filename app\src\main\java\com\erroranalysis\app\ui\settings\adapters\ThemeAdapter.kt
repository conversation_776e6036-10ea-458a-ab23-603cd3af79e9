package com.erroranalysis.app.ui.settings.adapters

import android.graphics.drawable.GradientDrawable
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.erroranalysis.app.databinding.ItemThemeBinding
import com.erroranalysis.app.ui.theme.AppTheme

/**
 * 主題選擇適配器
 */
class ThemeAdapter(
    private val themes: List<AppTheme>,
    private var currentTheme: AppTheme,
    private val onThemeSelected: (AppTheme) -> Unit
) : RecyclerView.Adapter<ThemeAdapter.ThemeViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ThemeViewHolder {
        val binding = ItemThemeBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return ThemeViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ThemeViewHolder, position: Int) {
        holder.bind(themes[position])
    }

    override fun getItemCount() = themes.size

    /**
     * 更新當前主題
     */
    fun updateCurrentTheme(theme: AppTheme) {
        val oldPosition = themes.indexOfFirst { it.id == currentTheme.id }
        val newPosition = themes.indexOfFirst { it.id == theme.id }
        
        currentTheme = theme
        
        if (oldPosition != -1) notifyItemChanged(oldPosition)
        if (newPosition != -1) notifyItemChanged(newPosition)
    }

    inner class ThemeViewHolder(
        private val binding: ItemThemeBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(theme: AppTheme) {
            // 設置主題信息
            binding.textThemeName.text = theme.name
            binding.textThemeDescription.text = theme.description
            binding.textThemeEmoji.text = theme.emoji

            // 創建漸變背景
            val gradient = GradientDrawable(
                GradientDrawable.Orientation.LEFT_RIGHT,
                intArrayOf(theme.getGradientStartColorInt(), theme.getGradientEndColorInt())
            )
            gradient.cornerRadius = 24f
            binding.viewThemePreview.background = gradient

            // 設置選中狀態
            val isSelected = theme.id == currentTheme.id
            binding.cardTheme.strokeWidth = if (isSelected) 6 else 0
            binding.cardTheme.strokeColor = theme.getPrimaryColorInt()
            
            // 顯示選中指示器
            binding.iconSelected.visibility = if (isSelected) {
                android.view.View.VISIBLE
            } else {
                android.view.View.GONE
            }

            // 設置點擊事件
            binding.root.setOnClickListener {
                if (!isSelected) {
                    onThemeSelected(theme)
                }
            }
        }
    }
}
