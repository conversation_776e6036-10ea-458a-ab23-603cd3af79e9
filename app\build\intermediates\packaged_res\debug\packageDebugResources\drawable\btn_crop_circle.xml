<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 外層白色邊框 -->
    <item>
        <shape android:shape="oval">
            <solid android:color="@color/text_white" />
        </shape>
    </item>
    
    <!-- 內層深色背景，創造朝內圓角效果 -->
    <item android:inset="1dp">
        <shape android:shape="oval">
            <solid android:color="@color/overlay_dark" />
        </shape>
    </item>
    
    <!-- 裁切圖標 -->
    <item 
        android:width="24dp"
        android:height="24dp"
        android:gravity="center">
        <vector
            android:width="24dp"
            android:height="24dp"
            android:viewportWidth="24"
            android:viewportHeight="24">
            <path
                android:fillColor="@color/text_white"
                android:pathData="M7,17V1H5v4H1v2h4v10c0,1.1 0.9,2 2,2h10v4h2v-4h4v-2H7zM7,15h8V7h2V5c0,-1.1 -0.9,-2 -2,-2H7v12z"/>
        </vector>
    </item>
    
</layer-list>
