# CSV簡化匯入指南

## 🎯 **新的簡化匯入規則**

### ✅ **最低要求（只需要其中一個）**
1. **題目文字** - 題目內容描述 **或**
2. **題目圖片** - 題目圖片檔名

### ⭕ **完全可選的欄位**
3. **題目ID** - 唯一識別碼（系統會自動生成）
4. **答案** - 題目答案（可以為空，等待AI生成）
5. **答案圖片** - 答案圖片檔名
6. **標籤** - 分類標籤
7. **難度** - 簡單/普通/困難
8. **說明** - 解題說明

## 🚀 **匯入變得超簡單**

**核心理念**：
- ✅ 只要有題目（文字或圖片），就能匯入
- ✅ 題目ID可以不填，系統自動生成
- ✅ 答案可以不填，後續用AI生成
- ✅ 其他欄位都是可選的

## 答案圖片欄位說明

### ✅ 正確用法

**1. 答案圖片為空（最常見）**：
```csv
題目ID,題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
A001,解方程式 x + 5 = 12,,x = 7,,一元一次方程式,簡單,基本移項法則
A002,計算 2 + 3 的值,,5,,基礎數學,簡單,基本加法運算
```

**2. 答案圖片有值**：
```csv
題目ID,題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
B001,解複雜方程式,complex_eq.jpg,"x = 1 或 x = 3",solution_steps.jpg,二次方程式,普通,配方法求解
B002,矩陣運算 A × B,matrix_ab.jpg,見解答圖片,matrix_result.jpg,線性代數,普通,矩陣乘法規則
```

**3. 混合使用**：
```csv
題目ID,題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
C001,簡單計算 1 + 1,,2,,基礎數學,簡單,
C002,複雜證明,theorem.jpg,見證明過程,proof_steps.jpg,幾何,困難,歐幾里得定理
C003,基本公式推導,,E = mc²,,物理,普通,愛因斯坦質能方程
```

## 📋 **超簡單範例檔案**

### 1. 最簡匯入（只有題目文字）
**檔案**：`docs/最簡匯入範例.csv`
```csv
題目文字
這是一個簡單的題目
計算 1 + 1 的值
解方程式 x = 5
什麼是重力加速度？
請說明牛頓第一定律
```

### 2. 只有圖片題目
**檔案**：`docs/圖片題目範例.csv`
```csv
題目圖片
math_problem_1.jpg
physics_diagram.jpg
chemistry_formula.jpg
geometry_question.png
biology_chart.jpg
```

### 3. 混合格式（靈活搭配）
**檔案**：`docs/簡化匯入範例.csv`
```csv
題目文字,題目圖片,答案,標籤,難度
解方程式 x + 5 = 12,,x = 7,一元一次方程式,簡單
,complex_equation.jpg,,二次方程式,困難
計算 2 + 3 的值,,5,基礎數學,
求導數 d/dx(x²),,2x,,
,geometry_problem.jpg,見圖解,幾何,普通
這是一個沒有答案的題目,,,待解答,
,unsolved_problem.jpg,,,
只有題目文字的簡單問題,,,,
,only_image.jpg,,,
```

### 4. 傳統完整格式（仍然支援）
**檔案**：`docs/數學-基礎代數.csv`
```csv
題目ID,題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
A001,解方程式 x + 5 = 12,,x = 7,,一元一次方程式,簡單,基本移項法則
A002,解方程式 2x - 3 = 7,,x = 5,,一元一次方程式,簡單,係數處理
A003,解方程式 x² - 4 = 0,,"x = 2 或 x = -2",,二次方程式,普通,平方差公式
```

## 使用場景

### 答案圖片適用情況

**✅ 建議使用答案圖片**：
- 數學證明步驟
- 幾何圖形解答
- 複雜計算過程
- 圖表分析結果
- 實驗數據圖表

**⭕ 可選使用**：
- 公式推導過程
- 解題思路圖解
- 概念示意圖

**❌ 不需要答案圖片**：
- 簡單數值計算
- 基本公式應用
- 純文字答案
- 選擇題答案

### 檔案結構範例

**包含答案圖片的完整結構**：
```
題庫包/
├── 數學-進階代數.csv
└── images/
    ├── complex_eq.jpg          (題目圖片)
    ├── solution_steps.jpg      (答案圖片)
    ├── limit_graph.jpg         (題目圖片)
    ├── limit_proof.jpg         (答案圖片)
    ├── integral_diagram.jpg    (題目圖片)
    ├── matrix_ab.jpg           (題目圖片)
    └── matrix_result.jpg       (答案圖片)
```

## 常見錯誤

### ❌ 錯誤示例

**1. 欄位數量不匹配**：
```csv
題目ID,題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
A001,解方程式 x + 5 = 12,x = 7,一元一次方程式,簡單  # 缺少欄位
```

**2. 必要欄位為空**：
```csv
題目ID,題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
A001,解方程式 x + 5 = 12,,,一元一次方程式,簡單,基本移項法則  # 答案為空
```

**3. 題目ID重複**：
```csv
題目ID,題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
A001,解方程式 x + 5 = 12,,x = 7,,一元一次方程式,簡單,基本移項法則
A001,解方程式 2x = 10,,x = 5,,一元一次方程式,簡單,係數處理  # ID重複
```

### ✅ 正確示例

**1. 完整欄位**：
```csv
題目ID,題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
A001,解方程式 x + 5 = 12,,x = 7,,一元一次方程式,簡單,基本移項法則
```

**2. 可選欄位為空**：
```csv
題目ID,題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
A001,解方程式 x + 5 = 12,,x = 7,,,,  # 標籤、難度、說明為空（允許）
```

**3. 唯一題目ID**：
```csv
題目ID,題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
A001,解方程式 x + 5 = 12,,x = 7,,一元一次方程式,簡單,基本移項法則
A002,解方程式 2x = 10,,x = 5,,一元一次方程式,簡單,係數處理
```

## 最佳實踐

### 1. 欄位使用建議
- **必要欄位**：確保不為空
- **可選欄位**：根據需要填寫，可以留空
- **答案圖片**：只在需要圖解時使用

### 2. 檔案組織
- 將相關的題目放在同一個CSV檔案中
- 使用有意義的檔案名稱
- 圖片檔案統一放在images目錄

### 3. 內容品質
- 確保答案準確
- 提供清晰的解題說明
- 使用一致的標籤分類

### 4. 技術要求
- 使用UTF-8編碼保存
- 確保CSV格式正確
- 圖片使用常見格式（jpg、png）

這個指南說明了答案圖片欄位確實是可選的，可以根據需要使用或留空！
