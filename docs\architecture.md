# 應用程式架構設計

## 🏗️ **整體架構**

### **MVVM 架構模式**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   View Layer    │    │  ViewModel      │    │  Model Layer    │
│   (Compose UI)  │◄──►│   Layer         │◄──►│  (Repository)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │  Data Layer     │
                                               │  (Managers)     │
                                               └─────────────────┘
```

### **分層職責**

**View Layer (UI 層)**
- 負責用戶界面渲染
- 處理用戶交互事件
- 觀察 ViewModel 狀態變化
- 使用 Jetpack Compose 構建

**ViewModel Layer (視圖模型層)**
- 管理 UI 相關的數據狀態
- 處理業務邏輯
- 與 Repository 層交互
- 提供數據給 UI 層

**Model Layer (模型層)**
- 定義數據結構
- 封裝業務規則
- 提供數據訪問接口

**Data Layer (數據層)**
- 管理數據存儲
- 處理數據持久化
- 提供數據操作 API

## 📁 **模組結構詳解**

### **UI 模組 (ui/)**

```
ui/
├── camera/                 # 拍照功能
│   ├── CameraScreen.kt     # 拍照主界面
│   ├── CameraViewModel.kt  # 拍照邏輯
│   └── components/         # 拍照相關組件
├── crop/                   # 裁切功能
│   ├── CropScreen.kt       # 裁切界面
│   ├── CropViewModel.kt    # 裁切邏輯
│   └── components/         # 裁切相關組件
├── deck/                   # 卡組管理
│   ├── DeckListScreen.kt   # 卡組列表
│   ├── DeckDetailScreen.kt # 卡組詳情
│   ├── DeckViewModel.kt    # 卡組邏輯
│   └── components/         # 卡組相關組件
├── card/                   # 卡片管理
│   ├── CardListScreen.kt   # 卡片列表
│   ├── CardEditScreen.kt   # 卡片編輯
│   ├── CardViewModel.kt    # 卡片邏輯
│   └── components/         # 卡片相關組件
├── import/                 # 匯入功能
│   ├── ImportScreen.kt     # 匯入界面
│   ├── ImportViewModel.kt  # 匯入邏輯
│   └── components/         # 匯入相關組件
└── components/             # 共用 UI 組件
    ├── CommonButton.kt     # 通用按鈕
    ├── CommonDialog.kt     # 通用對話框
    ├── LoadingIndicator.kt # 載入指示器
    └── ErrorMessage.kt     # 錯誤訊息
```

### **數據模組 (data/)**

```
data/
├── model/                  # 數據模型
│   ├── StudyCard.kt        # 學習卡片模型
│   ├── StudyDeck.kt        # 卡組模型
│   ├── CardDifficulty.kt   # 難度枚舉
│   └── ImportResult.kt     # 匯入結果模型
├── repository/             # 數據倉庫
│   ├── DeckRepository.kt   # 卡組數據倉庫
│   ├── CardRepository.kt   # 卡片數據倉庫
│   └── ImageRepository.kt  # 圖像數據倉庫
└── manager/                # 數據管理器
    ├── DeckDataManager.kt  # 卡組數據管理
    └── PreferencesManager.kt # 偏好設置管理
```

### **工具模組 (utils/)**

```
utils/
├── ImageStorageManager.kt  # 圖像存儲管理
├── BatchImportManager.kt   # 批量匯入管理
├── OpenCVHelper.kt         # OpenCV 工具
├── CameraHelper.kt         # 相機工具
├── FileUtils.kt            # 文件操作工具
└── Extensions.kt           # Kotlin 擴展函數
```

## 🔄 **數據流設計**

### **狀態管理流程**

```
User Action → UI Event → ViewModel → Repository → Data Manager → Storage
     ↑                                                                ↓
UI Update ← State Change ← ViewModel ← Repository ← Data Manager ← Storage
```

### **具體數據流示例**

**創建新卡組流程**：
1. 用戶在 UI 點擊"創建卡組"
2. UI 發送事件到 DeckViewModel
3. DeckViewModel 調用 DeckRepository.createDeck()
4. DeckRepository 調用 DeckDataManager.saveDeck()
5. DeckDataManager 將數據保存到 SharedPreferences
6. 數據變化通過 StateFlow 回傳到 UI
7. UI 更新顯示新的卡組

## 🎯 **設計模式應用**

### **單例模式 (Singleton)**
```kotlin
object DeckDataManager {
    private var instance: DeckDataManager? = null
    
    fun getInstance(context: Context): DeckDataManager {
        return instance ?: synchronized(this) {
            instance ?: DeckDataManager(context).also { instance = it }
        }
    }
}
```

### **觀察者模式 (Observer)**
```kotlin
class DeckViewModel : ViewModel() {
    private val _decks = MutableStateFlow<List<StudyDeck>>(emptyList())
    val decks: StateFlow<List<StudyDeck>> = _decks.asStateFlow()
    
    fun loadDecks() {
        viewModelScope.launch {
            repository.getDecks().collect { deckList ->
                _decks.value = deckList
            }
        }
    }
}
```

### **工廠模式 (Factory)**
```kotlin
object ViewModelFactory {
    fun createDeckViewModel(context: Context): DeckViewModel {
        val repository = DeckRepository(DeckDataManager.getInstance(context))
        return DeckViewModel(repository)
    }
}
```

### **策略模式 (Strategy)**
```kotlin
interface ImportStrategy {
    suspend fun import(inputStream: InputStream): ImportResult
}

class CsvImportStrategy : ImportStrategy {
    override suspend fun import(inputStream: InputStream): ImportResult {
        // CSV 匯入邏輯
    }
}

class JsonImportStrategy : ImportStrategy {
    override suspend fun import(inputStream: InputStream): ImportResult {
        // JSON 匯入邏輯
    }
}
```

## 🔧 **依賴注入設計**

### **手動依賴注入**
```kotlin
class AppContainer(private val context: Context) {
    
    // Data Managers
    val deckDataManager: DeckDataManager by lazy {
        DeckDataManager.getInstance(context)
    }
    
    val imageStorageManager: ImageStorageManager by lazy {
        ImageStorageManager(context)
    }
    
    // Repositories
    val deckRepository: DeckRepository by lazy {
        DeckRepository(deckDataManager)
    }
    
    val cardRepository: CardRepository by lazy {
        CardRepository(deckDataManager)
    }
    
    // ViewModels
    fun createDeckViewModel(): DeckViewModel {
        return DeckViewModel(deckRepository)
    }
    
    fun createCardViewModel(): CardViewModel {
        return CardViewModel(cardRepository, imageStorageManager)
    }
}
```

### **依賴注入使用**
```kotlin
class MainActivity : ComponentActivity() {
    private lateinit var appContainer: AppContainer
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        appContainer = AppContainer(this)
        
        setContent {
            ErrorAnalysisTheme {
                AppNavigation(appContainer)
            }
        }
    }
}
```

## 🎨 **UI 架構設計**

### **Compose 組件層次**
```
App
├── Theme (Material Design 3)
├── Navigation (NavHost)
├── Screens
│   ├── Screen Composables
│   ├── ViewModel Integration
│   └── State Management
└── Components
    ├── Reusable Components
    ├── Custom Components
    └── System Components
```

### **狀態提升原則**
```kotlin
@Composable
fun DeckListScreen(
    viewModel: DeckViewModel = hiltViewModel()
) {
    val decks by viewModel.decks.collectAsState()
    val uiState by viewModel.uiState.collectAsState()
    
    DeckListContent(
        decks = decks,
        uiState = uiState,
        onDeckClick = viewModel::selectDeck,
        onCreateDeck = viewModel::createDeck
    )
}

@Composable
fun DeckListContent(
    decks: List<StudyDeck>,
    uiState: DeckUiState,
    onDeckClick: (StudyDeck) -> Unit,
    onCreateDeck: () -> Unit
) {
    // UI 實現
}
```

## 🔄 **異步處理架構**

### **Coroutines 使用模式**
```kotlin
class DeckViewModel(
    private val repository: DeckRepository
) : ViewModel() {
    
    fun createDeck(name: String) {
        viewModelScope.launch {
            try {
                _uiState.value = DeckUiState.Loading
                val deck = repository.createDeck(name)
                _uiState.value = DeckUiState.Success(deck)
            } catch (e: Exception) {
                _uiState.value = DeckUiState.Error(e.message ?: "Unknown error")
            }
        }
    }
}
```

### **Flow 數據流**
```kotlin
class DeckRepository(
    private val dataManager: DeckDataManager
) {
    fun getDecks(): Flow<List<StudyDeck>> = flow {
        emit(dataManager.getAllDecks())
    }.flowOn(Dispatchers.IO)
    
    fun getDeck(id: String): Flow<StudyDeck?> = flow {
        emit(dataManager.getDeck(id))
    }.flowOn(Dispatchers.IO)
}
```

## 🧪 **測試架構**

### **測試金字塔**
```
        ┌─────────────┐
        │   UI Tests  │  ← 少量，端到端測試
        └─────────────┘
      ┌─────────────────┐
      │ Integration Tests│  ← 中等數量，模組間測試
      └─────────────────┘
    ┌─────────────────────┐
    │   Unit Tests        │  ← 大量，單元邏輯測試
    └─────────────────────┘
```

### **測試結構**
```
test/
├── unit/                   # 單元測試
│   ├── viewmodel/          # ViewModel 測試
│   ├── repository/         # Repository 測試
│   └── utils/              # 工具類測試
├── integration/            # 集成測試
│   ├── database/           # 數據庫測試
│   └── api/                # API 測試
└── ui/                     # UI 測試
    ├── screen/             # 螢幕測試
    └── component/          # 組件測試
```

這份架構文檔詳細說明了應用程式的設計模式、模組結構和數據流，為開發者提供了清晰的技術指導。
