# 數學式格式化功能指南

## 功能概述

為了解決Gemini AI回答中數學表達式不直觀的問題，我們開發了數學式格式化系統，能自動將LaTeX風格的數學表達式轉換為更易讀的Unicode符號格式。

## 問題解決

### 原始問題
Gemini回答中的數學式如：
```
\int [f(x) + g(x)]dx = \int f(x)dx + \int g(x)dx
\int c \cdot f(x)dx = c \cdot \int f(x)dx
```

### 格式化後效果
```
∫[f(x) + g(x)]dx = ∫f(x)dx + ∫f(x)dx  
∫c·f(x)dx = c·∫f(x)dx
```

## 支援的轉換

### 1. LaTeX符號轉換

| LaTeX | Unicode | 說明 |
|-------|---------|------|
| `\int` | `∫` | 積分符號 |
| `\sum` | `∑` | 求和符號 |
| `\prod` | `∏` | 連乘符號 |
| `\sqrt` | `√` | 根號 |
| `\pi` | `π` | 圓周率 |
| `\alpha` | `α` | 希臘字母α |
| `\beta` | `β` | 希臘字母β |
| `\theta` | `θ` | 希臘字母θ |
| `\lambda` | `λ` | 希臘字母λ |
| `\infty` | `∞` | 無窮大 |

### 2. 運算符號轉換

| LaTeX | Unicode | 說明 |
|-------|---------|------|
| `\cdot` | `·` | 點乘 |
| `\times` | `×` | 叉乘 |
| `\div` | `÷` | 除法 |
| `\pm` | `±` | 正負號 |
| `\leq` | `≤` | 小於等於 |
| `\geq` | `≥` | 大於等於 |
| `\neq` | `≠` | 不等於 |
| `\approx` | `≈` | 約等於 |

### 3. 指數和下標

| 格式 | 轉換後 | 說明 |
|------|--------|------|
| `x^2` | `x²` | 平方 |
| `x^3` | `x³` | 立方 |
| `x^-1` | `x⁻¹` | 負一次方 |
| `x_1` | `x₁` | 下標1 |
| `x_0` | `x₀` | 下標0 |

### 4. 集合符號

| LaTeX | Unicode | 說明 |
|-------|---------|------|
| `\in` | `∈` | 屬於 |
| `\subset` | `⊂` | 子集 |
| `\cup` | `∪` | 聯集 |
| `\cap` | `∩` | 交集 |
| `\emptyset` | `∅` | 空集合 |

## 實現方式

### 1. 改進的Gemini提示詞

在GeminiAIService中添加了詳細的數學表達式格式要求：

```kotlin
append("\n\n數學表達式格式要求：")
append("\n• 使用標準數學符號：×（乘法）、÷（除法）、²（平方）、³（立方）")
append("\n• 分數使用 a/b 格式")
append("\n• 根號使用 √ 符號")
append("\n• 積分使用 ∫ 符號")
append("\n• 希臘字母使用 π、α、β、θ 等")
append("\n• 上標下標使用 x² 、x₁ 格式")
append("\n• 避免使用 LaTeX 語法如 \\int、\\cdot 等")
```

### 2. MathFormatHelper工具類

核心功能：
- `formatMathExpression()` - 格式化數學表達式
- `createFormattedSpannable()` - 創建帶格式的文字
- `containsMathExpression()` - 檢測是否包含數學式

### 3. 自動格式化流程

```
Gemini原始回答 → MathFormatHelper.formatMathExpression() → 格式化後的回答
```

## 使用範例

### 積分題目
**原始回答**：
```
根據積分的線性性質，我們可以將和的積分拆分為各項的積分：
\int [f(x) + g(x)]dx = \int f(x)dx + \int g(x)dx
以及：\int c \cdot f(x)dx = c \cdot \int f(x)dx
```

**格式化後**：
```
根據積分的線性性質，我們可以將和的積分拆分為各項的積分：
∫[f(x) + g(x)]dx = ∫f(x)dx + ∫g(x)dx
以及：∫c·f(x)dx = c·∫f(x)dx
```

### 方程式題目
**原始回答**：
```
解二次方程式 x^2 - 5x + 6 = 0
使用因式分解法：(x-2)(x-3) = 0
所以 x = 2 或 x = 3
```

**格式化後**：
```
解二次方程式 x² - 5x + 6 = 0
使用因式分解法：(x-2)(x-3) = 0
所以 x = 2 或 x = 3
```

### 幾何題目
**原始回答**：
```
在直角三角形中，根據畢氏定理：
a^2 + b^2 = c^2
其中 c 是斜邊長度
```

**格式化後**：
```
在直角三角形中，根據畢氏定理：
a² + b² = c²
其中 c 是斜邊長度
```

## 技術特點

### 1. 智能檢測
- 自動檢測文本中是否包含數學表達式
- 只對包含數學符號的文本進行格式化
- 避免對普通文字造成影響

### 2. 漸進式處理
- 首先嘗試引導Gemini使用正確格式
- 然後對回答進行後處理格式化
- 雙重保障確保最佳顯示效果

### 3. 兼容性
- 支援純文字和圖文混合內容
- 與現有的RichTextEditText完全兼容
- 不影響其他功能的正常運作

## 效果對比

### 改進前
```
**理解積分的線性性質：**
積分運算具有線性性質，這表示我們可以將和的積分拆分為各項的積分，並且常數可以提出積分符號外。

即：$\int [f(x) + g(x)]dx = \int f(x)dx + \int g(x)dx$
以及：$\int c \cdot f(x)dx = c \cdot \int f(x)dx$ (其中 c 是常數)
```

### 改進後
```
**理解積分的線性性質：**
積分運算具有線性性質，這表示我們可以將和的積分拆分為各項的積分，並且常數可以提出積分符號外。

即：∫[f(x) + g(x)]dx = ∫f(x)dx + ∫g(x)dx
以及：∫c·f(x)dx = c·∫f(x)dx (其中 c 是常數)
```

## 使用建議

### 1. 最佳實踐
- 拍攝清晰的數學題目圖片
- 確保題目中的數學符號清楚可見
- 對於複雜的數學式，可以分步驟拍攝

### 2. 注意事項
- 格式化主要針對常見的數學符號
- 極複雜的數學表達式可能需要手動調整
- 建議在使用AI解答後檢查格式是否正確

### 3. 故障排除
- 如果格式化效果不理想，可以重新請求AI解答
- 對於特殊符號，可以在編輯時手動調整
- 複雜的分數和矩陣可能需要特殊處理

這個數學式格式化系統大幅改善了AI回答的可讀性，讓數學表達式在手機螢幕上更加清晰易讀！
