{"buildFiles": ["C:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\sdk\\libcxx_helper\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\sdk\\.cxx\\Debug\\5r5e101e\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\sdk\\.cxx\\Debug\\5r5e101e\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"opencv_jni_shared::@6890427a1f51a3e7e1df": {"artifactName": "opencv_jni_shared", "abi": "armeabi-v7a", "output": "C:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\sdk\\.cxx\\Debug\\5r5e101e\\armeabi-v7a\\libopencv_jni_shared.a", "runtimeFiles": []}}}