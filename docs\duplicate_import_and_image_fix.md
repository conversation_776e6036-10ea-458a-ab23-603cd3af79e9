# 重複匯入和圖片匯入問題修復

## 🔍 問題描述

用戶反映兩個問題：
1. **重複匯入問題**：過去匯入成功的內容會被重複匯入（例如"數學-進階"已匯入過，新匯入別的檔案時又被匯入一次）
2. **圖片匯入問題**：只有題目圖片的紀錄無法正確匯入

## 🕵️ 問題分析

### 1. 重複匯入問題

**根本原因**：
- 程式沒有檢查卡組名稱是否已存在
- 每次匯入都會創建新的卡組ID，即使名稱相同
- 缺乏重複檢查機制

**問題位置**：
```kotlin
// 原始程式碼 - 直接創建新卡組，沒有檢查重複
val deckId = dataManager.generateNewDeckId()
val deck = SimpleDeck(
    id = deckId,
    name = deckName,  // 沒有檢查是否已存在
    // ...
)
dataManager.addDeck(deck)
```

### 2. 圖片匯入問題

**可能原因**：
- 圖片檔案路徑不正確
- 圖片檔案不存在於ZIP的images目錄中
- 圖片解碼失敗
- 只有圖片沒有文字的題目處理邏輯不完善

## 🛠️ 修復措施

### 1. 重複匯入問題修復

#### 添加重複檢查機制
```kotlin
// 檢查是否已存在同名卡組
val existingDecks = dataManager.loadDecks()
val existingDeck = existingDecks.find { it.name == deckName }

if (existingDeck != null) {
    Log.w(TAG, "⚠️ 發現同名卡組：$deckName (ID: ${existingDeck.id})")
    Log.w(TAG, "   現有卡組有 ${existingDeck.cardCount} 張卡片")
    
    // 自動創建帶時間戳的新卡組名稱避免衝突
    val timestamp = SimpleDateFormat("yyyyMMdd_HHmm", Locale.getDefault())
        .format(Date())
    finalDeckName = "$deckName ($timestamp)"
    
    Log.i(TAG, "✅ 創建新卡組避免重複：$finalDeckName")
}
```

#### 用戶通知機制
```kotlin
// 如果創建了新名稱，添加說明到錯誤列表中
val finalErrors = if (finalDeckName != deckName) {
    errors + listOf("ℹ️ 因同名卡組已存在，已創建新卡組：$finalDeckName")
} else {
    errors
}
```

### 2. 圖片匯入問題修復

#### 增強圖片處理日誌
```kotlin
if (!imageName.isNullOrEmpty()) {
    Log.d(TAG, "處理圖片：$imageName")
    Log.d(TAG, "圖片目錄：${imagesDir?.absolutePath}")
    
    if (imagesDir != null) {
        val imageFile = File(imagesDir, imageName)
        Log.d(TAG, "圖片完整路徑：${imageFile.absolutePath}")
        Log.d(TAG, "圖片檔案是否存在：${imageFile.exists()}")
        
        if (imageFile.exists()) {
            Log.d(TAG, "圖片檔案大小：${imageFile.length()} bytes")
            // ... 處理圖片
        } else {
            Log.w(TAG, "⚠️ 圖片檔案不存在：$imageName")
            Log.w(TAG, "   檢查圖片目錄內容：")
            imagesDir.listFiles()?.forEach { file ->
                Log.w(TAG, "   - ${file.name}")
            }
        }
    }
}
```

#### 改進降級處理
```kotlin
// 降級處理：根據可用內容返回
when {
    text.isNotEmpty() -> {
        Log.d(TAG, "降級處理：返回純文字內容")
        "[{\"type\":\"text\",\"content\":\"$text\"}]"
    }
    !imageName.isNullOrEmpty() -> {
        Log.d(TAG, "降級處理：返回圖片佔位符")
        "[{\"type\":\"text\",\"content\":\"[圖片: $imageName]\"}]"
    }
    else -> {
        Log.w(TAG, "降級處理：返回空內容")
        ""
    }
}
```

## 📱 修復效果

### 重複匯入問題

**修復前**：
- ❌ 重複匯入相同名稱的卡組
- ❌ 用戶困惑為什麼有重複內容
- ❌ 無法區分不同版本的卡組

**修復後**：
- ✅ 自動檢測同名卡組
- ✅ 創建帶時間戳的新卡組名稱
- ✅ 明確通知用戶創建了新卡組
- ✅ 保留原有卡組不受影響

**範例**：
```
原卡組：數學-進階代數
新匯入：數學-進階代數 (20241219_1430)
通知：ℹ️ 因同名卡組已存在，已創建新卡組：數學-進階代數 (20241219_1430)
```

### 圖片匯入問題

**修復前**：
- ❌ 圖片匯入失敗時沒有詳細信息
- ❌ 只有圖片的題目可能無法正確處理
- ❌ 難以診斷圖片問題

**修復後**：
- ✅ 詳細的圖片處理日誌
- ✅ 列出圖片目錄內容幫助診斷
- ✅ 改進的降級處理機制
- ✅ 圖片佔位符確保內容不丟失

## 🔧 使用指南

### 1. 避免重複匯入

**最佳實踐**：
- 使用有意義的檔案名稱
- 定期檢查現有卡組
- 注意匯入結果中的通知信息

**如果出現重複**：
- 系統會自動創建新卡組名稱
- 檢查匯入結果中的說明
- 可以手動刪除不需要的卡組

### 2. 圖片匯入診斷

**檢查步驟**：
1. 確認ZIP檔案包含images目錄
2. 檢查圖片檔案名稱是否與CSV中一致
3. 確認圖片格式（JPG、PNG等）
4. 查看logcat日誌獲取詳細信息

**常見問題**：
- 圖片檔案名稱大小寫不匹配
- 圖片檔案不在images目錄中
- 圖片檔案損壞或格式不支援

## 📊 診斷工具

### 查看詳細日誌
```bash
adb logcat | grep "BatchImportManager"
```

### 關鍵日誌信息
- `⚠️ 發現同名卡組` - 檢測到重複
- `✅ 創建新卡組避免重複` - 成功處理重複
- `處理圖片` - 圖片處理開始
- `✅ 成功添加圖片` - 圖片處理成功
- `⚠️ 圖片檔案不存在` - 圖片檔案問題

## 🎯 結論

這次修復解決了兩個重要問題：

1. **重複匯入問題**：
   - 自動檢測同名卡組
   - 創建帶時間戳的新名稱
   - 保持資料完整性

2. **圖片匯入問題**：
   - 增強診斷能力
   - 改進錯誤處理
   - 提供降級方案

用戶現在可以：
- 安全地重複匯入檔案而不會產生重複內容
- 清楚了解圖片匯入的狀態
- 獲得詳細的診斷信息
- 享受更穩定的匯入體驗

這些修復提高了批次匯入功能的可靠性和用戶體驗。
