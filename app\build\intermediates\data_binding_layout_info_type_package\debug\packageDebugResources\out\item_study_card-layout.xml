<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_study_card" modulePackage="com.erroranalysis.app" filePath="app\src\main\res\layout\item_study_card.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_study_card_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="150" endOffset="51"/></Target><Target id="@+id/indicator_status" view="View"><Expressions/><location startLine="17" startOffset="8" endLine="21" endOffset="49"/></Target><Target id="@+id/text_question" view="TextView"><Expressions/><location startLine="31" startOffset="12" endLine="40" endOffset="42"/></Target><Target id="@+id/text_answer" view="TextView"><Expressions/><location startLine="53" startOffset="12" endLine="63" endOffset="43"/></Target><Target id="@+id/text_tags" view="TextView"><Expressions/><location startLine="66" startOffset="12" endLine="74" endOffset="43"/></Target><Target id="@+id/text_difficulty" view="TextView"><Expressions/><location startLine="84" startOffset="16" endLine="92" endOffset="45"/></Target><Target id="@+id/progress_mastery" view="ProgressBar"><Expressions/><location startLine="103" startOffset="20" endLine="110" endOffset="47"/></Target><Target id="@+id/text_mastery_level" view="TextView"><Expressions/><location startLine="112" startOffset="20" endLine="119" endOffset="49"/></Target><Target id="@+id/text_created_time" view="TextView"><Expressions/><location startLine="124" startOffset="16" endLine="131" endOffset="45"/></Target><Target id="@+id/text_stats" view="TextView"><Expressions/><location startLine="136" startOffset="12" endLine="144" endOffset="43"/></Target></Targets></Layout>