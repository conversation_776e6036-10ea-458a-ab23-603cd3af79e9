# 批次題庫匯入系統使用指南

## 系統概述

批次題庫匯入系統讓您可以在PC上準備大量題目，然後一次性匯入到Android App中，大幅提高題庫建立效率。

## 支援的檔案格式

### 方案1：ZIP壓縮包（推薦）
```
題庫包.zip
├── questions.json          # 題目資料
└── images/                 # 圖片資料夾
    ├── q001.jpg           # 題目圖片
    ├── q002.png
    └── q003.jpg
```

### 方案2：純JSON檔案
```
questions.json              # 僅包含文字題目
```

## JSON格式規範

### 完整格式範例
```json
{
  "version": "1.0",
  "deckName": "高中數學題庫",
  "deckDescription": "高中數學各章節練習題",
  "subject": "數學",
  "createdAt": "2024-01-15T10:30:00Z",
  "questions": [
    {
      "id": "q001",
      "questionText": "解下列方程式：x² - 5x + 6 = 0",
      "questionImage": "q001.jpg",
      "answer": "x = 2 或 x = 3",
      "answerImage": null,
      "tags": ["代數", "二次方程式"],
      "difficulty": "NORMAL",
      "explanation": "使用因式分解法：(x-2)(x-3) = 0"
    },
    {
      "id": "q002",
      "questionText": "",
      "questionImage": "q002.png",
      "answer": "30°",
      "answerImage": null,
      "tags": ["幾何", "三角形"],
      "difficulty": "EASY"
    }
  ]
}
```

### 重要說明：圖文混合內容處理

App內部使用JSON格式存儲圖文混合內容：
```json
[
  {"type": "text", "content": "這是文字內容"},
  {"type": "image", "content": "uuid-filename.jpg"},
  {"type": "text", "content": "更多文字"}
]
```

批次匯入時的轉換：
- questionText + questionImage → 自動轉換為圖文混合格式
- answer + answerImage → 自動轉換為圖文混合格式
- 圖片會自動保存到App的內部存儲中

### 欄位說明

| 欄位 | 必填 | 說明 | 範例 |
|------|------|------|------|
| version | 是 | 格式版本 | "1.0" |
| deckName | 是 | 卡組名稱 | "數學題庫" |
| deckDescription | 否 | 卡組描述 | "高中數學練習題" |
| subject | 否 | 學科主題 | "數學" |
| questions | 是 | 題目陣列 | [...] |

支援的學科主題：
- 數學/math → 藍色
- 物理/physics → 橙色  
- 化學/chemistry → 綠色
- 生物/biology → 青色
- 英文/english → 紅色
- 國文/chinese → 紫色
- 歷史/history → 棕色
- 地理/geography → 綠色
- 程式/programming → 橙紅色

題目欄位：

| 欄位 | 必填 | 說明 | 範例 |
|------|------|------|------|
| id | 是 | 題目ID | "q001" |
| questionText | 否 | 題目文字 | "解方程式..." |
| questionImage | 否 | 題目圖片檔名 | "q001.jpg" |
| answer | 是 | 答案 | "x = 2, 3" |
| answerImage | 否 | 答案圖片檔名 | "a001.jpg" |
| tags | 否 | 標籤陣列 | ["代數", "方程式"] |
| difficulty | 否 | 難度 | "EASY/NORMAL/HARD/VERY_HARD" |
| explanation | 否 | 解題說明 | "使用因式分解..." |

## PC端準備流程

### 步驟1：準備Excel表格

建立Excel檔案，包含以下欄位：

| 題目ID | 題目文字 | 題目圖片檔名 | 答案 | 答案圖片檔名 | 標籤 | 難度 | 說明 |
|--------|----------|--------------|------|--------------|------|------|------|
| q001 | 解方程式：x² - 5x + 6 = 0 | q001.jpg | x = 2 或 x = 3 | | 代數,方程式 | NORMAL | 因式分解法 |
| q002 | | q002.png | 30° | | 幾何,角度 | EASY | |
| q003 | 計算積分：∫x²dx | | x³/3 + C | | 微積分,積分 | HARD | 基本積分公式 |

### 步驟2：準備圖片檔案

1. 建立images資料夾
2. 放入圖片檔案：
   - 檔名必須與Excel中的檔名一致
   - 支援格式：JPG, PNG, GIF, BMP, WebP
   - 建議解析度：800x600以上
   - 檔案大小：建議小於5MB

### 步驟3：轉換為JSON

使用提供的Python腳本轉換Excel為JSON：

```bash
# 建立Excel模板
python excel_to_json.py template 數學題庫模板.xlsx

# 轉換Excel為JSON
python excel_to_json.py convert 數學題庫.xlsx "高中數學" ./題庫包 數學

# 打包ZIP檔案
python excel_to_json.py zip ./題庫包 數學題庫.zip
```

### 步驟4：打包ZIP檔案

1. 建立資料夾結構：
   ```
   題庫包/
   ├── questions.json
   └── images/
       ├── q001.jpg
       └── q002.png
   ```

2. 壓縮為ZIP：
   - 選擇整個資料夾
   - 右鍵 → 傳送到 → 壓縮的資料夾
   - 重新命名為有意義的名稱

## Android端匯入流程

### 步驟1：傳輸檔案到手機
- USB傳輸
- 雲端硬碟（Google Drive, OneDrive等）
- 即時通訊軟體傳檔

### 步驟2：執行匯入
1. 開啟錯題庫App
2. 長按右下角的➕按鈕
3. 選擇「批次匯入題庫」
4. 點擊「選擇題庫檔案」
5. 選擇ZIP或JSON檔案
6. 等待匯入完成

### 步驟3：檢查結果
- 查看匯入統計
- 檢查錯誤報告（如有）
- 確認新卡組已建立

## 注意事項

### 檔案要求
- JSON檔案必須是UTF-8編碼
- 圖片檔名不能包含特殊字元
- 建議圖片檔案小於5MB
- ZIP檔案大小建議小於100MB

### 常見問題
1. 圖片無法載入：檢查檔名是否一致
2. JSON格式錯誤：使用JSON驗證工具檢查
3. 匯入失敗：查看錯誤詳情，逐一修正

### 效能建議
- 單次匯入建議不超過500個題目
- 大量題目可分批匯入
- 圖片過多時考慮壓縮

## 最佳實踐

### 題庫組織
- 按章節或主題分組
- 使用有意義的卡組名稱
- 適當設置難度等級

### 圖片處理
- 統一圖片尺寸比例
- 確保文字清晰可讀
- 適當裁切去除無關內容

### 標籤系統
- 建立一致的標籤體系
- 使用階層式標籤（如：數學.代數.方程式）
- 避免過多標籤造成混亂

## 範例檔案

我們提供了完整的範例檔案供參考：
- 範例_數學題庫.xlsx - Excel模板
- 範例_題庫包.zip - 完整的ZIP包範例
- convert_excel.py - 轉換腳本

這個批次匯入系統讓您可以高效地建立大型題庫，特別適合教師、學生或培訓機構使用！
