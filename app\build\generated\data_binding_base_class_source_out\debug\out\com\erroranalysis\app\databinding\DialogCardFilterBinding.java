// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogCardFilterBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton btnApply;

  @NonNull
  public final MaterialButton btnClear;

  @NonNull
  public final Chip chipBeginner;

  @NonNull
  public final Chip chipFamiliar;

  @NonNull
  public final ChipGroup chipGroupMastery;

  @NonNull
  public final ChipGroup chipGroupTags;

  @NonNull
  public final Chip chipLearning;

  @NonNull
  public final Chip chipMastered;

  @NonNull
  public final Chip chipNotLearned;

  @NonNull
  public final Chip chipProficient;

  @NonNull
  public final TextInputEditText editKeyword;

  private DialogCardFilterBinding(@NonNull LinearLayout rootView, @NonNull MaterialButton btnApply,
      @NonNull MaterialButton btnClear, @NonNull Chip chipBeginner, @NonNull Chip chipFamiliar,
      @NonNull ChipGroup chipGroupMastery, @NonNull ChipGroup chipGroupTags,
      @NonNull Chip chipLearning, @NonNull Chip chipMastered, @NonNull Chip chipNotLearned,
      @NonNull Chip chipProficient, @NonNull TextInputEditText editKeyword) {
    this.rootView = rootView;
    this.btnApply = btnApply;
    this.btnClear = btnClear;
    this.chipBeginner = chipBeginner;
    this.chipFamiliar = chipFamiliar;
    this.chipGroupMastery = chipGroupMastery;
    this.chipGroupTags = chipGroupTags;
    this.chipLearning = chipLearning;
    this.chipMastered = chipMastered;
    this.chipNotLearned = chipNotLearned;
    this.chipProficient = chipProficient;
    this.editKeyword = editKeyword;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogCardFilterBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogCardFilterBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_card_filter, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogCardFilterBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_apply;
      MaterialButton btnApply = ViewBindings.findChildViewById(rootView, id);
      if (btnApply == null) {
        break missingId;
      }

      id = R.id.btn_clear;
      MaterialButton btnClear = ViewBindings.findChildViewById(rootView, id);
      if (btnClear == null) {
        break missingId;
      }

      id = R.id.chip_beginner;
      Chip chipBeginner = ViewBindings.findChildViewById(rootView, id);
      if (chipBeginner == null) {
        break missingId;
      }

      id = R.id.chip_familiar;
      Chip chipFamiliar = ViewBindings.findChildViewById(rootView, id);
      if (chipFamiliar == null) {
        break missingId;
      }

      id = R.id.chip_group_mastery;
      ChipGroup chipGroupMastery = ViewBindings.findChildViewById(rootView, id);
      if (chipGroupMastery == null) {
        break missingId;
      }

      id = R.id.chip_group_tags;
      ChipGroup chipGroupTags = ViewBindings.findChildViewById(rootView, id);
      if (chipGroupTags == null) {
        break missingId;
      }

      id = R.id.chip_learning;
      Chip chipLearning = ViewBindings.findChildViewById(rootView, id);
      if (chipLearning == null) {
        break missingId;
      }

      id = R.id.chip_mastered;
      Chip chipMastered = ViewBindings.findChildViewById(rootView, id);
      if (chipMastered == null) {
        break missingId;
      }

      id = R.id.chip_not_learned;
      Chip chipNotLearned = ViewBindings.findChildViewById(rootView, id);
      if (chipNotLearned == null) {
        break missingId;
      }

      id = R.id.chip_proficient;
      Chip chipProficient = ViewBindings.findChildViewById(rootView, id);
      if (chipProficient == null) {
        break missingId;
      }

      id = R.id.edit_keyword;
      TextInputEditText editKeyword = ViewBindings.findChildViewById(rootView, id);
      if (editKeyword == null) {
        break missingId;
      }

      return new DialogCardFilterBinding((LinearLayout) rootView, btnApply, btnClear, chipBeginner,
          chipFamiliar, chipGroupMastery, chipGroupTags, chipLearning, chipMastered, chipNotLearned,
          chipProficient, editKeyword);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
