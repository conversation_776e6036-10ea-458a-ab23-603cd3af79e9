# 空白頁面問題修復

## 問題描述

在移除滾動條改用ScrollView後，點擊檢視卡片出現全空白頁面。

## 問題原因分析

### 1. 佈局高度問題
**原始問題**：
```xml
<LinearLayout
    android:id="@+id/content_container"
    android:layout_height="wrap_content">  <!-- 問題所在 -->
    
    <MaterialCardView
        android:layout_height="0dp"
        android:layout_weight="1" />        <!-- 與父容器衝突 -->
</LinearLayout>
```

**問題說明**：
- 父容器使用`wrap_content`
- 子元素使用`layout_weight="1"`
- 這會導致佈局計算錯誤，子元素高度變為0

### 2. 內容載入問題
**可能的問題**：
- RichTextEditText的setRichContent()方法失敗
- ScrollView的滾動重置失敗
- 只讀模式設置失敗

## 修復方案

### 1. 修復佈局高度
```xml
<LinearLayout
    android:id="@+id/content_container"
    android:layout_height="match_parent">  <!-- 修復：使用match_parent -->
    
    <MaterialCardView
        android:layout_height="0dp"
        android:layout_weight="1" />        <!-- 現在可以正常工作 -->
</LinearLayout>
```

### 2. 加強內容載入錯誤處理
```kotlin
private fun loadCardContent() {
    try {
        // 先嘗試基本文字設置
        binding.editContent.setText(card.question)
        
        // 再嘗試只讀模式
        try {
            binding.editContent.setReadOnlyMode(true)
        } catch (e: Exception) {
            // 只讀模式失敗不影響基本顯示
        }
        
        // 最後嘗試富文本
        try {
            binding.editContent.setRichContent(card.question)
        } catch (e: Exception) {
            // 富文本失敗，保持基本文字
        }
        
    } catch (e: Exception) {
        // 最後的降級處理
        binding.editContent.setText("載入失敗：${e.message}")
    }
}
```

### 3. 改進滾動重置
```kotlin
private fun resetScrollPosition() {
    try {
        binding.scrollContent.post {
            try {
                binding.scrollContent.scrollTo(0, 0)
                binding.editContent.setSelection(0)
            } catch (e: Exception) {
                // 滾動重置失敗不影響內容顯示
            }
        }
    } catch (e: Exception) {
        // post失敗也不影響基本功能
    }
}
```

## 修復後的佈局結構

```xml
<LinearLayout (根容器)
    android:layout_height="match_parent">
    
    <!-- 標題區域 -->
    <LinearLayout android:layout_height="wrap_content">
        <TextView>題目</TextView>
        <TextView>點擊查看答案</TextView>
    </LinearLayout>
    
    <!-- 內容區域 -->
    <LinearLayout 
        android:id="@+id/content_container"
        android:layout_height="match_parent">  <!-- 修復：使用match_parent -->
        
        <MaterialCardView
            android:layout_height="0dp"
            android:layout_weight="1">         <!-- 現在可以正常展開 -->
            
            <ScrollView android:layout_height="match_parent">
                <LinearLayout android:padding="16dp">
                    <RichTextEditText 
                        android:layout_height="wrap_content"
                        android:minHeight="200dp" />
                </LinearLayout>
            </ScrollView>
            
        </MaterialCardView>
    </LinearLayout>
    
</LinearLayout>
```

## 錯誤處理改進

### 1. 分層錯誤處理
- **第一層**：基本文字設置（最可靠）
- **第二層**：只讀模式設置（可選）
- **第三層**：富文本設置（增強功能）

### 2. 降級策略
- 如果富文本失敗，使用基本文字
- 如果只讀模式失敗，仍可編輯但功能正常
- 如果滾動重置失敗，不影響內容顯示

### 3. 詳細日誌
```kotlin
android.util.Log.e("CardView", "基本文字設置成功")
android.util.Log.e("CardView", "只讀模式設置成功")
android.util.Log.e("CardView", "富文本內容載入成功")
```

## 測試要點

### 1. 基本顯示測試
- 點擊卡片後是否顯示內容
- 內容是否完整可見
- 佈局是否正確

### 2. 滾動功能測試
- 長內容是否可以滾動
- 滾動是否流暢
- 切換內容時滾動是否重置

### 3. 錯誤恢復測試
- 富文本載入失敗時是否有降級顯示
- 只讀模式失敗時是否仍可使用
- 滾動重置失敗時是否影響其他功能

## 日誌檢查

### 正常情況下的日誌
```
E/CardView: 開始載入卡片內容
E/CardView: 題目內容: [content_preview]...
E/CardView: 基本文字設置成功
E/CardView: 只讀模式設置成功
E/CardView: 富文本內容載入成功
E/CardView: ScrollView滾動位置重置成功
E/CardView: EditText游標位置重置成功
E/CardView: 卡片內容載入完成
```

### 異常情況下的日誌
```
E/CardView: 富文本載入失敗，使用基本文字
E/CardView: 只讀模式設置失敗，繼續使用基本模式
E/CardView: 重置滾動位置失敗
```

## 預防措施

### 1. 佈局設計原則
- 避免在wrap_content容器中使用layout_weight
- 確保容器有明確的高度定義
- 測試不同內容長度的顯示效果

### 2. 錯誤處理原則
- 關鍵功能要有降級方案
- 增強功能失敗不應影響基本功能
- 提供詳細的錯誤日誌

### 3. 測試策略
- 測試空內容、短內容、長內容
- 測試圖文混合內容
- 測試網路異常情況

這個修復應該能解決空白頁面問題，並提供更穩定的卡片檢視體驗！
