package com.erroranalysis.app.utils

import android.content.Context
import android.graphics.Bitmap
import android.util.Base64
import android.util.Log
import com.google.ai.client.generativeai.GenerativeModel
import com.google.ai.client.generativeai.type.content
import com.google.ai.client.generativeai.type.generationConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream

/**
 * Gemini AI服務類
 * 用於調用Google Gemini Pro Vision API進行圖片問題解答
 */
class GeminiAIService(private val context: Context) {
    
    companion object {
        private const val TAG = "GeminiAIService"
        private const val API_KEY = "AIzaSyBWM1nmkkC1iaE_qw2ETJy9_U2p6wQWUtk"
        private const val MODEL_NAME = "gemini-2.5-flash"
    }
    
    private val generativeModel = GenerativeModel(
        modelName = MODEL_NAME,
        apiKey = API_KEY,
        generationConfig = generationConfig {
            temperature = 0.4f
            topK = 32
            topP = 1f
            maxOutputTokens = 4096
        }
    )
    
    /**
     * 解答圖片中的題目
     * @param questionText 題目文字內容
     * @param images 題目中的圖片列表
     * @return AI解答結果
     */
    suspend fun solveQuestion(questionText: String, images: List<Bitmap>): String {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "開始調用Gemini API解答題目")
                Log.d(TAG, "題目文字: $questionText")
                Log.d(TAG, "圖片數量: ${images.size}")
                
                // 構建提示詞
                val prompt = buildPrompt(questionText)
                
                // 構建內容
                val inputContent = content {
                    text(prompt)

                    // 添加圖片
                    images.forEach { bitmap ->
                        image(bitmap)
                    }
                }
                
                // 調用API
                val response = generativeModel.generateContent(inputContent)
                val rawResult = response.text ?: "AI無法生成回答"

                Log.d(TAG, "Gemini API調用成功")
                Log.d(TAG, "原始回答長度: ${rawResult.length}")
                Log.d(TAG, "原始回答預覽: ${rawResult.take(100)}...")

                // 格式化數學表達式
                val formattedResult = MathFormatHelper.formatMathExpression(rawResult)

                Log.d(TAG, "格式化後預覽: ${formattedResult.take(100)}...")

                formattedResult
                
            } catch (e: Exception) {
                Log.e(TAG, "Gemini API調用失敗", e)
                "AI解答失敗：${e.message}"
            }
        }
    }
    
    /**
     * 構建提示詞
     */
    private fun buildPrompt(questionText: String): String {
        return buildString {
            append("請解答圖片中的題目。")

            if (questionText.isNotBlank()) {
                append("\n\n題目文字內容：\n")
                append(questionText)
            }

            append("\n\n請提供詳細的解答步驟和最終答案。")

            append("\n\n回答格式要求：")
            append("\n• 使用清晰的段落分隔，每個步驟之間空一行")
            append("\n• 重要的數學式單獨成行")
            append("\n• 使用標題或編號來組織內容結構")
            append("\n• 最終答案要明確標示")

            append("\n\n數學表達式格式要求：")
            append("\n• 使用標準數學符號：×（乘法）、÷（除法）、²（平方）、³（立方）")
            append("\n• 分數使用 a/b 格式")
            append("\n• 根號使用 √ 符號")
            append("\n• 積分使用 ∫ 符號")
            append("\n• 希臘字母使用 π、α、β、θ 等")
            append("\n• 上標下標使用 x² 、x₁ 格式")
            append("\n• 避免使用 LaTeX 語法如 \\int、\\cdot 等")

            append("\n\n範例格式：")
            append("\n**步驟1：分析題目**")
            append("\n題目要求解...")
            append("\n")
            append("\n**步驟2：應用公式**")
            append("\n∫(2x + 3)dx = x² + 3x + C")
            append("\n")
            append("\n**最終答案：**")
            append("\nx² + 3x + C")

            append("\n\n請確保回答結構清晰，數學式易讀，適合在手機上顯示。")
        }
    }
    
    /**
     * 將Bitmap轉換為ByteArray
     */
    private fun bitmapToByteArray(bitmap: Bitmap): ByteArray? {
        return try {
            val outputStream = ByteArrayOutputStream()
            bitmap.compress(Bitmap.CompressFormat.JPEG, 80, outputStream)
            outputStream.toByteArray()
        } catch (e: Exception) {
            Log.e(TAG, "圖片轉換ByteArray失敗", e)
            null
        }
    }
    
    /**
     * 測試API連接（僅文字，不含圖片）
     */
    suspend fun testApiConnection(): String {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "測試API連接...")

                val testContent = content {
                    text("請回答：1+1等於多少？")
                }

                val response = generativeModel.generateContent(testContent)
                val result = response.text ?: "無回應"

                Log.d(TAG, "API測試成功，回應: $result")
                "API測試成功：$result"

            } catch (e: Exception) {
                Log.e(TAG, "API測試失敗", e)
                "API測試失敗：${e.message}"
            }
        }
    }

    /**
     * 檢查API是否可用
     */
    fun isApiAvailable(): Boolean {
        return API_KEY.isNotBlank()
    }
}
