<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_template_preview"
            android:layout_width="60dp"
            android:layout_height="80dp"
            android:layout_marginEnd="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/text_template_icon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📘"
                    android:textColor="@color/white"
                    android:textSize="24sp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/text_template_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="高中數學"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/text_template_subject"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:background="@drawable/chip_background"
                android:paddingHorizontal="8dp"
                android:paddingVertical="2dp"
                android:text="數學"
                android:textColor="@color/primary"
                android:textSize="10sp" />

            <TextView
                android:id="@+id/text_template_tags"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="代數 • 幾何 • 統計"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/text_template_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="收集高中數學各章節錯題"
                android:textSize="12sp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
