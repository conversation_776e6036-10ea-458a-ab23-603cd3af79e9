<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_camera" modulePackage="com.erroranalysis.app" filePath="app\src\main\res\layout\activity_camera.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_camera_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="109" endOffset="51"/></Target><Target id="@+id/viewFinder" view="androidx.camera.view.PreviewView"><Expressions/><location startLine="10" startOffset="4" endLine="17" endOffset="51"/></Target><Target id="@+id/tv_focus_indicator" view="TextView"><Expressions/><location startLine="27" startOffset="8" endLine="39" endOffset="40"/></Target><Target id="@+id/btn_back" view="ImageButton"><Expressions/><location startLine="42" startOffset="8" endLine="51" endOffset="46"/></Target><Target id="@+id/layout_camera_controls" view="LinearLayout"><Expressions/><location startLine="56" startOffset="4" endLine="107" endOffset="18"/></Target><Target id="@+id/btn_gallery" view="ImageButton"><Expressions/><location startLine="67" startOffset="8" endLine="74" endOffset="46"/></Target><Target id="@+id/btn_capture" view="View"><Expressions/><location startLine="77" startOffset="8" endLine="84" endOffset="38"/></Target><Target id="@+id/btn_logs" view="ImageButton"><Expressions/><location startLine="87" startOffset="8" endLine="95" endOffset="46"/></Target><Target id="@+id/btn_settings" view="ImageButton"><Expressions/><location startLine="98" startOffset="8" endLine="105" endOffset="46"/></Target></Targets></Layout>