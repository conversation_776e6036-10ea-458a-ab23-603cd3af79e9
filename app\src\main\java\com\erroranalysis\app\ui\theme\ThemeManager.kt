package com.erroranalysis.app.ui.theme

import android.content.Context
import android.content.SharedPreferences

/**
 * 主題管理器
 * 負責主題的保存、載入和應用
 */
class ThemeManager private constructor(context: Context) {
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private var currentTheme: AppTheme = ThemeCollection.defaultTheme
    
    companion object {
        private const val PREFS_NAME = "theme_preferences"
        private const val KEY_CURRENT_THEME = "current_theme_id"
        
        @Volatile
        private var INSTANCE: ThemeManager? = null
        
        fun getInstance(context: Context): ThemeManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ThemeManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    init {
        loadCurrentTheme()
    }
    
    /**
     * 載入當前主題
     */
    private fun loadCurrentTheme() {
        val themeId = prefs.getString(KEY_CURRENT_THEME, ThemeCollection.defaultTheme.id)
        currentTheme = ThemeCollection.getThemeById(themeId ?: ThemeCollection.defaultTheme.id)
    }
    
    /**
     * 獲取當前主題
     */
    fun getCurrentTheme(): AppTheme {
        return currentTheme
    }
    
    /**
     * 設置當前主題
     */
    fun setCurrentTheme(theme: AppTheme) {
        currentTheme = theme
        prefs.edit().putString(KEY_CURRENT_THEME, theme.id).apply()
        
        // 通知主題變更監聽器
        themeChangeListeners.forEach { it.onThemeChanged(theme) }
    }
    
    /**
     * 根據ID設置主題
     */
    fun setCurrentTheme(themeId: String) {
        val theme = ThemeCollection.getThemeById(themeId)
        setCurrentTheme(theme)
    }
    
    /**
     * 獲取所有可用主題
     */
    fun getAllThemes(): List<AppTheme> {
        return ThemeCollection.getAllThemes()
    }
    
    /**
     * 檢查是否為當前主題
     */
    fun isCurrentTheme(theme: AppTheme): Boolean {
        return currentTheme.id == theme.id
    }
    
    // 主題變更監聽器
    private val themeChangeListeners = mutableListOf<ThemeChangeListener>()
    
    /**
     * 添加主題變更監聽器
     */
    fun addThemeChangeListener(listener: ThemeChangeListener) {
        themeChangeListeners.add(listener)
    }
    
    /**
     * 移除主題變更監聽器
     */
    fun removeThemeChangeListener(listener: ThemeChangeListener) {
        themeChangeListeners.remove(listener)
    }
    
    /**
     * 主題變更監聽器接口
     */
    interface ThemeChangeListener {
        fun onThemeChanged(theme: AppTheme)
    }
}

/**
 * 主題應用工具類
 */
object ThemeUtils {
    
    /**
     * 應用主題到Activity
     */
    fun applyTheme(context: Context, theme: AppTheme) {
        // 這裡可以動態修改顏色資源
        // 由於Android的限制，我們主要通過程式碼動態設置顏色
    }
    
    /**
     * 獲取主題的主色調
     */
    fun getPrimaryColor(context: Context): Int {
        return ThemeManager.getInstance(context).getCurrentTheme().getPrimaryColorInt()
    }
    
    /**
     * 獲取主題的深色主色調
     */
    fun getPrimaryDarkColor(context: Context): Int {
        return ThemeManager.getInstance(context).getCurrentTheme().getPrimaryDarkColorInt()
    }
    
    /**
     * 獲取主題的強調色
     */
    fun getAccentColor(context: Context): Int {
        return ThemeManager.getInstance(context).getCurrentTheme().getAccentColorInt()
    }
    
    /**
     * 獲取主題的漸變起始色
     */
    fun getGradientStartColor(context: Context): Int {
        return ThemeManager.getInstance(context).getCurrentTheme().getGradientStartColorInt()
    }
    
    /**
     * 獲取主題的漸變結束色
     */
    fun getGradientEndColor(context: Context): Int {
        return ThemeManager.getInstance(context).getCurrentTheme().getGradientEndColorInt()
    }

    /**
     * 獲取主題的背景色
     */
    fun getBackgroundColor(context: Context): Int {
        return ThemeManager.getInstance(context).getCurrentTheme().getBackgroundColorInt()
    }

    /**
     * 獲取主題的卡片背景色
     */
    fun getCardBackgroundColor(context: Context): Int {
        return ThemeManager.getInstance(context).getCurrentTheme().getCardBackgroundColorInt()
    }
}
