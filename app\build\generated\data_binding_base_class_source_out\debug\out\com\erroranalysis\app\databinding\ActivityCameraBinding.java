// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.camera.view.PreviewView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityCameraBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageButton btnBack;

  @NonNull
  public final View btnCapture;

  @NonNull
  public final ImageButton btnGallery;

  @NonNull
  public final ImageButton btnLogs;

  @NonNull
  public final ImageButton btnSettings;

  @NonNull
  public final LinearLayout layoutCameraControls;

  @NonNull
  public final TextView tvFocusIndicator;

  @NonNull
  public final PreviewView viewFinder;

  private ActivityCameraBinding(@NonNull ConstraintLayout rootView, @NonNull ImageButton btnBack,
      @NonNull View btnCapture, @NonNull ImageButton btnGallery, @NonNull ImageButton btnLogs,
      @NonNull ImageButton btnSettings, @NonNull LinearLayout layoutCameraControls,
      @NonNull TextView tvFocusIndicator, @NonNull PreviewView viewFinder) {
    this.rootView = rootView;
    this.btnBack = btnBack;
    this.btnCapture = btnCapture;
    this.btnGallery = btnGallery;
    this.btnLogs = btnLogs;
    this.btnSettings = btnSettings;
    this.layoutCameraControls = layoutCameraControls;
    this.tvFocusIndicator = tvFocusIndicator;
    this.viewFinder = viewFinder;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityCameraBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityCameraBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_camera, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityCameraBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_back;
      ImageButton btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btn_capture;
      View btnCapture = ViewBindings.findChildViewById(rootView, id);
      if (btnCapture == null) {
        break missingId;
      }

      id = R.id.btn_gallery;
      ImageButton btnGallery = ViewBindings.findChildViewById(rootView, id);
      if (btnGallery == null) {
        break missingId;
      }

      id = R.id.btn_logs;
      ImageButton btnLogs = ViewBindings.findChildViewById(rootView, id);
      if (btnLogs == null) {
        break missingId;
      }

      id = R.id.btn_settings;
      ImageButton btnSettings = ViewBindings.findChildViewById(rootView, id);
      if (btnSettings == null) {
        break missingId;
      }

      id = R.id.layout_camera_controls;
      LinearLayout layoutCameraControls = ViewBindings.findChildViewById(rootView, id);
      if (layoutCameraControls == null) {
        break missingId;
      }

      id = R.id.tv_focus_indicator;
      TextView tvFocusIndicator = ViewBindings.findChildViewById(rootView, id);
      if (tvFocusIndicator == null) {
        break missingId;
      }

      id = R.id.viewFinder;
      PreviewView viewFinder = ViewBindings.findChildViewById(rootView, id);
      if (viewFinder == null) {
        break missingId;
      }

      return new ActivityCameraBinding((ConstraintLayout) rootView, btnBack, btnCapture, btnGallery,
          btnLogs, btnSettings, layoutCameraControls, tvFocusIndicator, viewFinder);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
