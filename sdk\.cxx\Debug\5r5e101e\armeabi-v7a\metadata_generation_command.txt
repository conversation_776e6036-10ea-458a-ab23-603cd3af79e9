                        -HC:\augment-projects\HomeWork\android\ErrorAnalysisApp\sdk\libcxx_helper
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\augment-projects\HomeWork\android\ErrorAnalysisApp\sdk\build\intermediates\cxx\Debug\5r5e101e\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\augment-projects\HomeWork\android\ErrorAnalysisApp\sdk\build\intermediates\cxx\Debug\5r5e101e\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-BC:\augment-projects\HomeWork\android\ErrorAnalysisApp\sdk\.cxx\Debug\5r5e101e\armeabi-v7a
-GNinja
-DANDROID_STL=c++_shared
                        Build command args: []
                        Version: 2