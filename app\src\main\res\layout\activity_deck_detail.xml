<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/text_card_count"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:background="@drawable/circle_number_background"
                        android:gravity="center"
                        android:text="0"
                        android:textColor="@android:color/white"
                        android:textSize="12sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/text_deck_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:text="卡組名稱"
                        android:textColor="@android:color/white"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                </LinearLayout>

                <!-- 篩選按鈕 -->
                <ImageButton
                    android:id="@+id/btn_filter"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="篩選卡片"
                    android:padding="12dp"
                    android:src="@drawable/ic_filter"
                    android:tint="@android:color/white" />

            </LinearLayout>

        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_cards"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <LinearLayout
                android:id="@+id/layout_empty"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="48dp"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📝"
                    android:textSize="64sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="還沒有卡片"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="點擊右下角的 + 按鈕建立第一張卡片"
                    android:textSize="14sp" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_create_card"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_add" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
