<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.erroranalysis.app" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="321" endOffset="51"/></Target><Target id="@+id/layout_header" view="LinearLayout"><Expressions/><location startLine="10" startOffset="4" endLine="37" endOffset="18"/></Target><Target id="@+id/btn_camera" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="65" startOffset="20" endLine="98" endOffset="71"/></Target><Target id="@+id/btn_error_bank" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="100" startOffset="20" endLine="133" endOffset="71"/></Target><Target id="@+id/btn_report" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="144" startOffset="20" endLine="177" endOffset="71"/></Target><Target id="@+id/btn_practice" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="179" startOffset="20" endLine="212" endOffset="71"/></Target><Target id="@+id/tv_analyzed_count" view="TextView"><Expressions/><location startLine="257" startOffset="28" endLine="264" endOffset="58"/></Target><Target id="@+id/tv_error_count" view="TextView"><Expressions/><location startLine="282" startOffset="28" endLine="289" endOffset="58"/></Target><Target id="@+id/bottom_navigation" view="com.google.android.material.bottomnavigation.BottomNavigationView"><Expressions/><location startLine="311" startOffset="4" endLine="319" endOffset="44"/></Target></Targets></Layout>