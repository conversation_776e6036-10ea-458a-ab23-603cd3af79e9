# 深度診斷測試指南

## 問題現狀

用戶報告：
- 點擊卡片後直接跳回錯題庫畫面
- 即使建立全新卡片與卡組仍有問題
- 使用logcat看不到任何訊息

這表明CardViewActivity可能根本沒有啟動，或者在啟動過程中立即崩潰。

## 診斷策略

### 階段1：測試簡化版本

現在已創建TestCardViewActivity，這是一個極簡版本的卡片檢視：
- 使用簡單的TextView顯示
- 最少的初始化邏輯
- 詳細的日誌記錄

### 階段2：日誌檢查命令

請使用以下命令查看日誌：

#### 基本日誌檢查
```bash
adb logcat | grep -E "(TestCardView|DeckDetail)"
```

#### 完整錯誤日誌
```bash
adb logcat | grep -E "(FATAL|AndroidRuntime|ERROR)"
```

#### 所有相關日誌
```bash
adb logcat | grep -E "(CardView|TestCardView|DeckDetail|Intent|Activity)"
```

#### 清除日誌後重新測試
```bash
adb logcat -c
adb logcat | grep -E "(TestCardView|DeckDetail|FATAL|ERROR)"
```

## 測試步驟

### 步驟1：測試簡化版本

1. **安裝新版本APK**
2. **清除logcat**：`adb logcat -c`
3. **開始監控**：`adb logcat | grep -E "(TestCardView|DeckDetail)"`
4. **執行操作**：
   - 打開應用程式
   - 進入任意卡組
   - 點擊任意卡片
5. **觀察結果**：
   - 是否進入TestCardViewActivity？
   - 是否看到相關日誌？

### 步驟2：期望的日誌輸出

#### 正常情況下應該看到：
```
E/DeckDetail: === 開始viewCard ===
E/DeckDetail: 準備啟動CardViewActivity
E/DeckDetail: 卡片ID: [card_id]
E/DeckDetail: 卡組ID: [deck_id]
E/DeckDetail: Intent創建成功
E/DeckDetail: Intent數據添加完成
E/DeckDetail: 準備調用startActivity
E/DeckDetail: startActivity調用完成
E/DeckDetail: === viewCard結束 ===
E/TestCardView: === TestCardViewActivity onCreate 開始 ===
E/TestCardView: super.onCreate 完成
E/TestCardView: setContentView 完成
E/TestCardView: 工具列設置完成
E/TestCardView: 卡片數據: [card_id]
E/TestCardView: 卡組ID: [deck_id]
E/TestCardView: 內容設置完成
E/TestCardView: === onCreate 完成 ===
E/TestCardView: onStart 調用
E/TestCardView: onResume 調用
```

#### 如果沒有看到任何日誌：
這表明：
1. ADB連接有問題
2. 應用程式沒有正確安裝
3. 點擊事件沒有觸發

#### 如果只看到DeckDetail日誌，沒有TestCardView日誌：
這表明：
1. Intent啟動失敗
2. TestCardViewActivity沒有正確註冊
3. 系統級別的問題

### 步驟3：故障排除

#### 情況A：完全沒有日誌
```bash
# 檢查ADB連接
adb devices

# 檢查應用程式是否運行
adb shell ps | grep erroranalysis

# 檢查logcat是否工作
adb logcat -d | tail -10
```

#### 情況B：只有DeckDetail日誌
```bash
# 檢查Activity是否註冊
adb shell dumpsys package com.erroranalysis.app | grep TestCardViewActivity

# 檢查Intent過濾器
adb shell am start -n com.erroranalysis.app/.ui.study.TestCardViewActivity
```

#### 情況C：有錯誤日誌
```bash
# 查看完整錯誤
adb logcat | grep -A 10 -B 10 "FATAL\|AndroidRuntime"
```

## 可能的問題和解決方案

### 問題1：Activity未註冊
**症狀**：DeckDetail日誌正常，但沒有TestCardView日誌
**解決**：檢查AndroidManifest.xml中的Activity註冊

### 問題2：Intent數據問題
**症狀**：Activity啟動但立即結束
**解決**：檢查Parcelable實現和Intent數據

### 問題3：佈局問題
**症狀**：onCreate開始但在setContentView時失敗
**解決**：檢查佈局文件和資源

### 問題4：權限問題
**症狀**：系統拒絕啟動Activity
**解決**：檢查應用程式權限和安全設置

### 問題5：記憶體問題
**症狀**：應用程式被系統殺死
**解決**：重啟設備，清理記憶體

## 進階診斷

### 使用ADB直接測試
```bash
# 直接啟動TestCardViewActivity
adb shell am start -n com.erroranalysis.app/.ui.study.TestCardViewActivity

# 檢查Activity堆疊
adb shell dumpsys activity activities | grep -A 5 -B 5 TestCardView
```

### 檢查系統資源
```bash
# 檢查記憶體使用
adb shell dumpsys meminfo com.erroranalysis.app

# 檢查CPU使用
adb shell top -n 1 | grep erroranalysis
```

### 檢查應用程式狀態
```bash
# 檢查應用程式詳細信息
adb shell dumpsys package com.erroranalysis.app

# 檢查Activity狀態
adb shell dumpsys activity com.erroranalysis.app
```

## 測試結果分析

### 結果1：TestCardViewActivity正常工作
**結論**：原始CardViewActivity有特定問題
**下一步**：逐步恢復原始Activity的功能

### 結果2：TestCardViewActivity也無法工作
**結論**：系統級別問題或基礎配置問題
**下一步**：檢查應用程式基礎設置

### 結果3：間歇性工作
**結論**：可能是記憶體或時序問題
**下一步**：添加延遲和重試機制

## 回復原始功能

如果TestCardViewActivity工作正常，可以逐步恢復原始功能：

1. **恢復原始Activity**：
   ```kotlin
   // 在DeckDetailActivity中改回
   val intent = Intent(this, CardViewActivity::class.java)
   ```

2. **逐步添加功能**：
   - 先只載入基本佈局
   - 再添加內容載入
   - 最後添加滾動和交互功能

3. **定位具體問題**：
   - 哪個步驟導致失敗
   - 哪個組件有問題
   - 哪個資源缺失

這個深度診斷應該能幫助我們找到問題的根本原因！
