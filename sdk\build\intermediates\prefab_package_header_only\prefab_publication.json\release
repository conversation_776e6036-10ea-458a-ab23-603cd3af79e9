{"installationFolder": "C:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\sdk\\build\\intermediates\\prefab_package\\release\\prefab", "gradlePath": ":opencv", "packageInfo": {"packageName": "opencv", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "opencv_jni_shared", "moduleHeaders": "C:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\sdk\\native\\jni\\include", "moduleExportLibraries": [], "abis": []}]}}