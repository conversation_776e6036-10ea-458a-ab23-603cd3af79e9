# CSV格式完整指南

## 標準CSV格式

### 完整欄位結構
```csv
題目ID,題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
```

### 欄位說明

| 欄位名稱 | 是否必要 | 說明 | 範例 |
|----------|----------|------|------|
| **題目ID** | ✅ 必要 | 唯一識別碼 | Q001, P001, E001 |
| **題目文字** | ⭕ 可選 | 題目內容描述 | 計算 2 + 3 的值 |
| **題目圖片** | ⭕ 可選 | 圖片檔名（放在images目錄） | equation.jpg, diagram.png |
| **答案** | ✅ 必要 | 題目答案 | 5, x = 2 或 x = 3 |
| **答案圖片** | ⭕ 可選 | 答案圖片檔名 | solution.jpg, steps.png |
| **標籤** | ⭕ 可選 | 分類標籤（逗號分隔） | 基礎數學,代數 |
| **難度** | ⭕ 可選 | 難度等級 | 簡單, 普通, 困難 |
| **說明** | ⭕ 可選 | 解題說明或提示 | 使用因式分解法 |

## 範例檔案

### 1. 數學題庫範例
**檔案名稱：** `數學-基礎代數.csv`

```csv
題目ID,題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
Q001,計算 2 + 3 的值,,5,,基礎數學,簡單,基本加法運算
Q002,解方程式 x + 5 = 12,,x = 7,,代數,簡單,一元一次方程式
Q006,解方程式 x² = 16,quadratic_eq.jpg,x = ±4,,代數,普通,二次方程式
Q010,解方程式 x² - 5x + 6 = 0,factoring_example.jpg,"x = 2 或 x = 3",solution_steps.jpg,代數,普通,二次方程式因式分解
```

### 2. 物理題庫範例
**檔案名稱：** `物理-力學基礎.csv`

```csv
題目ID,題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
P001,一個物體從靜止開始自由落下，經過2秒後的速度是多少？（g=10m/s²）,,20 m/s,,運動學,簡單,自由落體運動公式 v = gt
P002,質量為2kg的物體受到10N的力作用，求其加速度,force_diagram.jpg,5 m/s²,calculation_steps.jpg,牛頓定律,簡單,牛頓第二定律 F = ma
P003,一個彈簧的彈性係數為100N/m，壓縮0.1m時的彈性勢能是多少？,spring_diagram.jpg,0.5 J,energy_formula.jpg,能量,普通,彈性勢能公式 E = ½kx²
```

## 檔案結構

### 包含圖片的完整結構
```
題庫包/
├── 數學-基礎代數.csv
├── 物理-力學基礎.csv
└── images/
    ├── quadratic_eq.jpg
    ├── factoring_example.jpg
    ├── solution_steps.jpg
    ├── force_diagram.jpg
    ├── calculation_steps.jpg
    ├── spring_diagram.jpg
    └── energy_formula.jpg
```

### 只有文字的簡單結構
```
數學-基礎代數.csv
物理-力學基礎.csv
英文-基礎文法.csv
```

## 欄位變體支援

### ID欄位變體
- `題目ID`
- `ID`
- `id`
- `題目id`

### 答案欄位變體
- `答案`
- `answer`
- `Answer`

### 圖片欄位變體
- `題目圖片`
- `圖片檔名`
- `答案圖片`

### 其他欄位變體
- `題目文字` / `題目`
- `標籤` / `tag`
- `難度` / `difficulty`
- `說明` / `解釋`

## 格式要求

### 1. 編碼格式
- **必須使用UTF-8編碼**
- 避免使用BOM（Byte Order Mark）
- 確保中文字符正確顯示

### 2. 分隔符號
- 使用逗號（,）作為欄位分隔符
- 如果內容包含逗號，請用雙引號包圍
- 範例：`"x = 2, y = 3"`

### 3. 空值處理
- 可選欄位可以留空
- 空值不需要特殊標記
- 範例：`Q001,計算題,,5,,數學,簡單,`

### 4. 特殊字符
- 支援中文、英文、數字
- 支援數學符號：±、×、÷、∫、∑等
- 支援換行符（在雙引號內）

## 常見錯誤

### 1. 缺少必要欄位
❌ **錯誤：**
```csv
ID,題目,答案
Q001,計算題,5
```

✅ **正確：**
```csv
題目ID,題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
Q001,計算題,,5,,數學,簡單,
```

### 2. 欄位數量不匹配
❌ **錯誤：**
```csv
題目ID,題目文字,答案
Q001,計算題,5,額外欄位
```

✅ **正確：**
```csv
題目ID,題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
Q001,計算題,,5,,數學,簡單,
```

### 3. 編碼問題
❌ **錯誤：** 使用ANSI或GBK編碼
✅ **正確：** 使用UTF-8編碼

### 4. 圖片路徑錯誤
❌ **錯誤：** `images/diagram.jpg`
✅ **正確：** `diagram.jpg`（圖片檔案放在images目錄下）

## 匯入流程

### 1. 準備檔案
1. 創建CSV檔案，使用UTF-8編碼
2. 確保包含必要欄位（題目ID、答案）
3. 如有圖片，放在images目錄下

### 2. 匯入步驟
1. 打開錯題庫應用程式
2. 點擊「批次匯入」功能
3. 選擇CSV檔案
4. 等待匯入完成

### 3. 錯誤處理
- 檢查錯誤信息
- 修正CSV格式
- 重新匯入

## 最佳實踐

### 1. 檔案命名
- 使用有意義的名稱：`科目-章節.csv`
- 避免特殊字符和空格
- 範例：`數學-代數基礎.csv`

### 2. 內容組織
- 同一主題的題目放在同一檔案
- 使用一致的標籤分類
- 合理設置難度等級

### 3. 圖片管理
- 使用描述性的檔案名稱
- 保持適當的圖片大小
- 使用常見的圖片格式（jpg、png）

### 4. 品質控制
- 檢查拼寫和語法
- 確保答案正確
- 提供清晰的解題說明

這個完整的CSV格式指南應該能幫助用戶正確創建和匯入題庫檔案！
