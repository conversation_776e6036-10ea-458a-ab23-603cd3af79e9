# "國小數學.csv" 神秘檔案診斷指南

## 🔍 問題描述

用戶反映在批次匯入題目zip檔時，會憑空出現"國小數學.csv"的題庫，不確定是程式內建還是從其他地方來的。

## 🕵️ 調查結果

經過詳細的程式碼檢查，**確認應用程式中沒有任何內建的"國小數學.csv"檔案**：

### ✅ 已檢查的位置
- `app/src/main/assets/` - 不存在
- `app/src/main/res/raw/` - 不存在  
- `docs/` 資料夾 - 只有範例檔案（數學-基礎代數.csv等）
- 程式碼中的硬編碼字串 - 無"國小數學"相關內容
- 預設資料初始化 - 只有空的預設卡組，無預設題目

### 🎯 可能的來源

1. **用戶實際選擇了該檔案**
   - 檔案選擇器中確實存在"國小數學.csv"
   - 可能是之前從其他地方下載或創建的

2. **檔案選擇器快取問題**
   - Android系統檔案選擇器可能快取了之前的檔案
   - 顯示的是上次選擇的檔案

3. **雲端同步檔案**
   - Google Drive、OneDrive等雲端服務同步的檔案
   - 檔案名稱可能在同步過程中被修改

4. **其他應用程式分享**
   - 從其他教育類應用程式分享的檔案
   - 通過即時通訊軟體接收的檔案

5. **下載檔案**
   - 從網站下載的範例檔案
   - 瀏覽器下載資料夾中的檔案

## 🛠️ 診斷工具

我已經在程式中添加了詳細的日誌記錄功能，可以幫助追蹤檔案來源：

### 新增的日誌功能
```kotlin
// 在 BatchImportManager.kt 中添加了詳細追蹤
Log.d(TAG, "=== 檔案名稱追蹤開始 ===")
Log.d(TAG, "URI: $uri")
Log.d(TAG, "URI Scheme: ${uri.scheme}")
Log.d(TAG, "URI Authority: ${uri.authority}")
Log.d(TAG, "URI Path: ${uri.path}")

// 特別檢查"國小數學.csv"
if (name == "國小數學.csv") {
    Log.w(TAG, "⚠️ 檢測到'國小數學.csv'檔案！")
    Log.w(TAG, "   這可能是：")
    Log.w(TAG, "   1. 用戶實際選擇了此檔案")
    Log.w(TAG, "   2. 檔案選擇器快取問題")
    Log.w(TAG, "   3. 從其他應用分享的檔案")
    Log.w(TAG, "   4. 雲端下載的檔案")
}
```

## 📱 用戶診斷步驟

### 步驟1：檢查logcat日誌
1. 連接手機到電腦
2. 開啟Android Studio或使用adb命令
3. 執行匯入操作
4. 查看logcat中的詳細日誌：
   ```bash
   adb logcat | grep "BatchImportManager"
   ```

### 步驟2：檢查檔案來源
查看日誌中的URI信息：
- **content://com.android.providers.downloads.documents/** - 下載檔案
- **content://com.google.android.apps.docs.storage/** - Google Drive
- **content://com.microsoft.skydrive.content.external/** - OneDrive
- **content://media/external/** - 本地存儲

### 步驟3：清除快取
1. 進入手機設置
2. 應用程式管理 → 智學分析
3. 存儲 → 清除快取
4. 重新嘗試匯入

### 步驟4：檢查檔案管理器
1. 開啟檔案管理器
2. 搜尋"國小數學.csv"
3. 檢查檔案位置和創建時間
4. 查看檔案內容確認來源

## 🔧 解決方案

### 方案1：重新選擇檔案
1. 確認要匯入的正確檔案位置
2. 清除應用程式快取
3. 重新開啟檔案選擇器
4. 仔細選擇正確的檔案

### 方案2：檢查檔案內容
1. 用文字編輯器開啟"國小數學.csv"
2. 檢查內容是否為期望的題目
3. 如果內容正確，可以繼續使用
4. 如果內容不正確，刪除該檔案

### 方案3：使用正確的檔案
1. 使用docs資料夾中的範例檔案
2. 或創建新的CSV檔案
3. 確保檔案名稱和內容都正確

## 📋 預防措施

### 1. 檔案命名規範
- 使用有意義的檔案名稱
- 避免使用通用名稱如"數學.csv"
- 建議格式：`科目-章節-日期.csv`

### 2. 檔案管理
- 定期清理下載資料夾
- 使用專門的資料夾存放題庫檔案
- 避免從不明來源下載檔案

### 3. 應用程式維護
- 定期清除應用程式快取
- 檢查雲端同步設定
- 注意檔案分享來源

## 🎯 結論

"國小數學.csv"檔案**不是程式內建的**，而是來自外部來源。通過新增的診斷日誌功能，用戶可以：

1. **追蹤檔案真實來源**
2. **識別檔案選擇器問題**
3. **確認檔案內容正確性**
4. **避免未來類似問題**

如果問題持續發生，建議用戶：
- 檢查logcat日誌獲取詳細信息
- 清除應用程式快取
- 使用已知正確的檔案進行測試
- 聯繫開發者提供日誌信息以進一步診斷
