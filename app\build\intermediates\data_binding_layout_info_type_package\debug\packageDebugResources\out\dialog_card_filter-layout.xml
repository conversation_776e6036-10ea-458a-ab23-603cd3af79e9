<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_card_filter" modulePackage="com.erroranalysis.app" filePath="app\src\main\res\layout\dialog_card_filter.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_card_filter_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="148" endOffset="14"/></Target><Target id="@+id/edit_keyword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="26" startOffset="8" endLine="33" endOffset="59"/></Target><Target id="@+id/chip_group_mastery" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="47" startOffset="4" endLine="103" endOffset="48"/></Target><Target id="@+id/chip_not_learned" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="55" startOffset="8" endLine="61" endOffset="53"/></Target><Target id="@+id/chip_beginner" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="63" startOffset="8" endLine="69" endOffset="53"/></Target><Target id="@+id/chip_learning" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="71" startOffset="8" endLine="77" endOffset="53"/></Target><Target id="@+id/chip_familiar" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="79" startOffset="8" endLine="85" endOffset="53"/></Target><Target id="@+id/chip_proficient" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="87" startOffset="8" endLine="93" endOffset="53"/></Target><Target id="@+id/chip_mastered" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="95" startOffset="8" endLine="101" endOffset="53"/></Target><Target id="@+id/chip_group_tags" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="115" startOffset="4" endLine="121" endOffset="37"/></Target><Target id="@+id/btn_clear" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="130" startOffset="8" endLine="137" endOffset="55"/></Target><Target id="@+id/btn_apply" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="139" startOffset="8" endLine="144" endOffset="58"/></Target></Targets></Layout>