                        -HC:\augment-projects\HomeWork\android\ErrorAnalysisApp\sdk\libcxx_helper
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-<PERSON>ANDROID_PLATFORM=android-24
-<PERSON>ANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\augment-projects\HomeWork\android\ErrorAnalysisApp\sdk\build\intermediates\cxx\Debug\5r5e101e\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\augment-projects\HomeWork\android\ErrorAnalysisApp\sdk\build\intermediates\cxx\Debug\5r5e101e\obj\x86_64
-DCMAKE_BUILD_TYPE=Debug
-BC:\augment-projects\HomeWork\android\ErrorAnalysisApp\sdk\.cxx\Debug\5r5e101e\x86_64
-GNinja
-DANDROID_STL=c++_shared
                        Build command args: []
                        Version: 2