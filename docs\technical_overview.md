# 技術文檔總覽

## 📚 **完整文檔體系**

本文檔集合為錯題庫應用程式提供了全面的技術指導，涵蓋從架構設計到具體實現的各個層面。

### **📋 文檔清單**

| 序號 | 文檔名稱 | 文件路徑 | 主要內容 | 目標讀者 |
|------|----------|----------|----------|----------|
| 1 | **應用程式概覽** | `app_overview.md` | 整體介紹、技術棧、核心特性 | 所有開發者 |
| 2 | **架構設計** | `architecture.md` | MVVM架構、設計模式、數據流 | 架構師、高級開發者 |
| 3 | **功能模組** | `features.md` | 各功能模組詳細實現 | 功能開發者 |
| 4 | **數據管理** | `data_management.md` | 數據存儲、模型設計、管理策略 | 後端開發者 |
| 5 | **API 參考** | `api_reference.md` | 工具類、組件API、使用示例 | 所有開發者 |
| 6 | **開發指南** | `development_guide.md` | 環境設置、編碼規範、最佳實踐 | 新手開發者 |
| 7 | **CSV匯入指南** | `csv_optional_fields_guide.md` | CSV格式、匯入規則、範例 | 數據處理開發者 |

## 🎯 **技術架構總覽**

### **整體架構**
```
┌─────────────────────────────────────────────────────────────┐
│                    錯題庫應用程式                              │
├─────────────────────────────────────────────────────────────┤
│  UI Layer (Jetpack Compose)                                │
│  ├── Camera/Crop Screens    ├── Deck Management            │
│  ├── Card Management        ├── Import Screens             │
│  └── Study Screens          └── Common Components          │
├─────────────────────────────────────────────────────────────┤
│  ViewModel Layer                                            │
│  ├── CameraViewModel        ├── DeckViewModel              │
│  ├── CardViewModel          ├── ImportViewModel            │
│  └── StudyViewModel         └── Base ViewModels            │
├─────────────────────────────────────────────────────────────┤
│  Repository Layer                                           │
│  ├── DeckRepository         ├── CardRepository             │
│  ├── ImageRepository        └── ImportRepository           │
├─────────────────────────────────────────────────────────────┤
│  Data Layer                                                 │
│  ├── DeckDataManager        ├── ImageStorageManager        │
│  ├── BatchImportManager     └── PreferencesManager         │
├─────────────────────────────────────────────────────────────┤
│  Utils Layer                                                │
│  ├── OpenCVHelper           ├── CameraHelper               │
│  ├── FileUtils              └── Extensions                 │
├─────────────────────────────────────────────────────────────┤
│  Storage Layer                                              │
│  ├── SharedPreferences      ├── Internal Files             │
│  ├── JSON Files             └── Image Files                │
└─────────────────────────────────────────────────────────────┘
```

### **核心技術棧**
- **UI框架**：Jetpack Compose
- **架構模式**：MVVM + Repository
- **圖像處理**：OpenCV 4.9.0
- **相機功能**：CameraX
- **數據存儲**：SharedPreferences + JSON
- **異步處理**：Kotlin Coroutines
- **依賴注入**：手動DI

## 🔧 **核心組件概覽**

### **主要管理器**

**DeckDataManager**
- 職責：卡組和卡片數據的CRUD操作
- 特點：單例模式，線程安全
- 存儲：SharedPreferences + JSON文件

**ImageStorageManager**
- 職責：圖像文件的存儲和管理
- 特點：自動壓縮，統一命名
- 存儲：內部存儲目錄

**BatchImportManager**
- 職責：CSV和JSON格式的批量匯入
- 特點：支援多種格式，錯誤處理
- 規則：簡化匯入條件

**OpenCVHelper**
- 職責：圖像處理和計算機視覺
- 特點：邊界檢測，透視變換
- 功能：梯形校正，圖像增強

### **數據模型**

**StudyDeck（卡組）**
```kotlin
data class StudyDeck(
    val id: String,                    // 唯一識別碼
    val name: String,                  // 卡組名稱
    val description: String,           // 描述
    val icon: String,                  // 圖標
    val color: String,                 // 顏色
    val cardCount: Int,                // 卡片數量
    val createdAt: Long,               // 創建時間
    val updatedAt: Long                // 更新時間
)
```

**StudyCard（卡片）**
```kotlin
data class StudyCard(
    val id: String,                    // 唯一識別碼
    val deckId: String,                // 所屬卡組
    val question: String,              // 題目（JSON格式）
    val answer: String,                // 答案（JSON格式）
    val tags: List<String>,            // 標籤
    val difficulty: CardDifficulty,    // 難度
    val studiedCount: Int,             // 學習次數
    val correctCount: Int,             // 正確次數
    val createdAt: Long,               // 創建時間
    val updatedAt: Long                // 更新時間
)
```

## 📱 **功能模組概覽**

### **拍照與圖像處理**
- **拍照功能**：CameraX實現，支援前後鏡頭
- **梯形校正**：OpenCV邊界檢測和透視變換
- **圖像裁切**：自定義裁切工具，支援自由和固定比例

### **錯題庫系統**
- **卡組管理**：創建、編輯、刪除、統計
- **卡片管理**：圖文混合內容、標籤、難度
- **學習模式**：複習算法、進度追蹤

### **批量匯入**
- **CSV匯入**：簡化格式，只需題目文字或圖片
- **JSON匯入**：完整格式支援
- **ZIP匯入**：包含圖片的批量匯入

### **圖像存儲**
- **統一管理**：自動命名、壓縮優化
- **緩存機制**：提高載入性能
- **清理功能**：自動清理未使用圖片

## 🗄️ **數據存儲策略**

### **存儲層次**
```
Application Data/
├── SharedPreferences/          # 應用設置和索引
│   ├── deck_list              # 卡組列表
│   ├── user_settings          # 用戶設置
│   └── app_preferences        # 應用偏好
├── Internal Files/            # 內部文件存儲
│   ├── decks/                 # 卡組數據
│   │   ├── deck_001.json
│   │   └── deck_002.json
│   └── images/                # 圖像文件
│       ├── img_001.jpg
│       └── img_002.jpg
└── Cache/                     # 臨時緩存
    ├── temp_images/
    └── processing/
```

### **數據格式**

**圖文混合內容**
```json
[
  {
    "type": "text",
    "content": "解方程式："
  },
  {
    "type": "image",
    "content": "equation_001.jpg"
  }
]
```

**CSV匯入格式**
```csv
題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
解方程式 x + 5 = 12,,x = 7,,一元一次方程式,簡單,基本移項法則
```

## 🧪 **測試策略**

### **測試金字塔**
```
     ┌─────────────┐
     │  UI Tests   │  ← 少量，端到端
     └─────────────┘
   ┌─────────────────┐
   │Integration Tests│  ← 中等，模組間
   └─────────────────┘
 ┌─────────────────────┐
 │   Unit Tests        │  ← 大量，單元邏輯
 └─────────────────────┘
```

### **測試覆蓋**
- **單元測試**：ViewModel、Repository、Utils
- **集成測試**：數據管理器、匯入功能
- **UI測試**：關鍵用戶流程

## 🚀 **性能優化**

### **圖像處理優化**
- 異步處理：使用Coroutines
- 內存管理：及時回收Bitmap
- 壓縮策略：智能壓縮算法

### **UI性能優化**
- 懶加載：列表項目按需載入
- 狀態管理：避免不必要的重組
- 動畫優化：流暢的過渡效果

### **存儲優化**
- 數據分片：大型卡組分片存儲
- 索引優化：快速查詢和過濾
- 緩存策略：常用數據緩存

## 🔒 **安全與隱私**

### **數據安全**
- 本地存儲：所有數據存儲在應用私有目錄
- 無網絡權限：完全離線運行
- 數據加密：敏感數據加密存儲

### **隱私保護**
- 無外部存儲訪問
- 無位置信息收集
- 無用戶數據上傳

## 📦 **構建與部署**

### **構建配置**
- **Debug**：開發調試版本
- **Release**：生產發布版本
- **架構支援**：僅arm64-v8a（優化APK大小）

### **版本管理**
- **語義化版本**：主版本.次版本.修訂版本
- **Git工作流**：功能分支開發
- **自動化測試**：CI/CD集成

## 🔮 **未來規劃**

### **功能擴展**
- AI答案生成：集成機器學習模型
- 雲端同步：可選的雲端備份
- 多語言支援：國際化功能

### **技術升級**
- Compose多平台：支援其他平台
- 模組化架構：功能模組化
- 性能監控：應用性能分析

## 📞 **開發支援**

### **文檔使用指南**
1. **新手開發者**：app_overview.md → development_guide.md → api_reference.md
2. **功能開發者**：architecture.md → features.md → data_management.md
3. **架構師**：architecture.md → data_management.md → api_reference.md

### **常見問題**
- 環境設置問題：參考development_guide.md
- API使用問題：參考api_reference.md
- 架構理解問題：參考architecture.md

### **技術支援**
- 詳細文檔：查看對應模組文檔
- 代碼示例：參考API參考文檔
- 最佳實踐：參考開發指南

---

**文檔維護**：隨代碼更新同步維護
**版本控制**：與主分支版本保持一致
**更新頻率**：重大功能變更時更新

這份技術文檔總覽為開發者提供了完整的技術指導體系，確保項目的可維護性和可擴展性。
