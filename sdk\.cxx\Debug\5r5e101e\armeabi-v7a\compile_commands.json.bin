C/C++ Build Metadata                A           c         z                                rC:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe   '--target=armv7-none-linux-androideabi24   t--sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot   -g   	-DANDROID   -fdata-sections   -ffunction-sections   -funwind-tables   -fstack-protector-strong   -no-canonical-prefixes   -D_FORTIFY_SOURCE=2   -march=armv7-a   -mthumb   -Wformat   -Werror=format-security   -fno-limit-debug-info   -fPIC   YC:\augment-projects\HomeWork\android\ErrorAnalysisApp\sdk\.cxx\Debug\5r5e101e\armeabi-v7a   opencv_jni_shared   QC:\augment-projects\HomeWork\android\ErrorAnalysisApp\sdk\libcxx_helper\dummy.cpp   ,CMakeFiles\opencv_jni_shared.dir\dummy.cpp.o                              	   
         
            