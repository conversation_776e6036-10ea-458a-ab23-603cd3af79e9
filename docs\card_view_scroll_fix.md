# 卡片檢視滾動問題修復

## 問題描述

在卡片檢視功能中，當用戶：
1. 進入檢視答案UI
2. 向下滾動查看答案內容
3. 切換回查看題目
4. 發現題目無法顯示，需要手動向上滾動才能看到

## 問題原因分析

### 1. 滾動位置保持
- EditText在內容切換時保持之前的滾動位置
- 答案內容通常比題目長，用戶滾動到底部
- 切換到題目時，滾動位置仍在底部

### 2. 內容長度差異
- 題目內容可能較短
- 滾動位置超出題目內容範圍
- 導致題目內容在視窗外，看不到

### 3. 缺少滾動重置
- 原始代碼沒有在內容切換時重置滾動位置
- 用戶需要手動調整滾動條才能看到內容

## 修復方案

### 1. 添加滾動位置重置函數
```kotlin
private fun resetScrollPosition() {
    binding.editContent.post {
        try {
            // 滾動到頂部
            binding.editContent.scrollTo(0, 0)
            
            // 如果是ScrollView，也重置其滾動位置
            val parent = binding.editContent.parent
            if (parent is android.widget.ScrollView) {
                parent.scrollTo(0, 0)
            }
            
            // 設置游標到開始位置
            binding.editContent.setSelection(0)
            
            android.util.Log.d("CardView", "滾動位置已重置到頂部")
        } catch (e: Exception) {
            android.util.Log.e("CardView", "重置滾動位置失敗", e)
        }
    }
}
```

### 2. 在內容切換時調用重置
```kotlin
private fun toggleContent() {
    // ... 內容切換邏輯 ...
    
    if (showingAnswer) {
        val combinedContent = buildCombinedAnswerContent()
        binding.editContent.setRichContent(combinedContent)
    } else {
        binding.editContent.setRichContent(card.question)
    }
    
    // 重置滾動位置到頂部
    resetScrollPosition()
    
    updateUI()
}
```

### 3. 在初始載入時也重置
```kotlin
private fun loadCardContent() {
    try {
        binding.editContent.setReadOnlyMode(true)
        binding.editContent.setRichContent(card.question)
        
        // 確保初始載入時滾動位置在頂部
        resetScrollPosition()
    } catch (e: Exception) {
        // 錯誤處理...
        resetScrollPosition()
    }
}
```

## 技術細節

### 1. 使用post()確保時機正確
```kotlin
binding.editContent.post {
    // 在UI更新完成後執行滾動重置
    binding.editContent.scrollTo(0, 0)
}
```
- `post()`確保在內容載入完成後執行
- 避免在內容尚未渲染時嘗試滾動

### 2. 多層次滾動處理
```kotlin
// EditText本身的滾動
binding.editContent.scrollTo(0, 0)

// 父容器ScrollView的滾動
val parent = binding.editContent.parent
if (parent is android.widget.ScrollView) {
    parent.scrollTo(0, 0)
}
```
- 處理EditText內部滾動
- 處理外層ScrollView滾動
- 確保完整重置

### 3. 游標位置重置
```kotlin
binding.editContent.setSelection(0)
```
- 將游標設置到文本開始位置
- 確保用戶看到內容的開頭

### 4. 錯誤處理
```kotlin
try {
    // 滾動重置邏輯
} catch (e: Exception) {
    android.util.Log.e("CardView", "重置滾動位置失敗", e)
}
```
- 防止滾動重置失敗影響其他功能
- 記錄錯誤日誌便於調試

## 修復效果

### 修復前的問題流程
```
1. 用戶查看題目（滾動位置：頂部）
2. 切換到答案（滾動位置：頂部）
3. 用戶向下滾動查看答案（滾動位置：底部）
4. 切換回題目（滾動位置：仍在底部）
5. 題目不可見，需要手動向上滾動
```

### 修復後的正常流程
```
1. 用戶查看題目（滾動位置：頂部）
2. 切換到答案（滾動位置：自動重置到頂部）
3. 用戶向下滾動查看答案（滾動位置：底部）
4. 切換回題目（滾動位置：自動重置到頂部）
5. 題目立即可見，無需手動調整
```

## 用戶體驗改進

### 1. 無縫切換
- 每次內容切換都自動重置到頂部
- 用戶無需手動調整滾動位置
- 立即看到新內容的開頭

### 2. 一致性體驗
- 無論之前滾動到哪裡，切換後都從頂部開始
- 提供可預期的行為模式
- 減少用戶困惑

### 3. 提高效率
- 減少手動滾動操作
- 快速查看內容開頭
- 更流暢的學習體驗

## 測試建議

### 1. 基本功能測試
- 載入卡片，確認題目在頂部顯示
- 切換到答案，確認答案在頂部顯示
- 來回切換多次，確認每次都重置到頂部

### 2. 滾動行為測試
- 在答案模式下滾動到底部
- 切換到題目，確認題目立即可見
- 在題目模式下滾動（如果內容夠長）
- 切換到答案，確認答案從頂部開始

### 3. 邊界情況測試
- 測試很短的題目和很長的答案
- 測試很長的題目和很短的答案
- 測試只有圖片的內容
- 測試圖文混合的長內容

### 4. 錯誤處理測試
- 測試內容載入失敗的情況
- 確認即使在錯誤情況下滾動也能正確重置

## 相關改進

### 1. 性能優化
- 使用post()避免不必要的重複調用
- 只在內容切換時重置，不影響正常滾動

### 2. 日誌記錄
- 添加詳細的日誌記錄
- 便於調試和問題追蹤

### 3. 兼容性
- 處理不同類型的滾動容器
- 確保在各種設備上都能正常工作

## 未來考慮

### 1. 記憶滾動位置
- 可以考慮為每種內容類型記憶滾動位置
- 在某些場景下可能更有用

### 2. 平滑滾動
- 可以使用平滑滾動動畫
- 提供更好的視覺效果

### 3. 智能滾動
- 根據內容長度智能決定是否需要重置
- 提供更個性化的體驗

這個修復確保了卡片檢視功能的滾動行為符合用戶期望，提供了更流暢的學習體驗！
