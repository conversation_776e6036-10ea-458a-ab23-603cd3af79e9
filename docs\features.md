# 功能模組詳細說明

## 📸 **拍照與圖像處理模組**

### **拍照功能 (Camera Module)**

**核心文件**：
- `ui/camera/CameraScreen.kt` - 拍照界面
- `ui/camera/CameraViewModel.kt` - 拍照邏輯
- `utils/CameraHelper.kt` - 相機工具類

**主要功能**：
- ✅ 使用 CameraX 進行拍照
- ✅ 支援前後鏡頭切換
- ✅ 自動對焦和曝光
- ✅ 拍照預覽和確認
- ✅ 圖像質量優化

**技術實現**：
```kotlin
class CameraViewModel : ViewModel() {
    private val _cameraState = MutableStateFlow(CameraState.Ready)
    val cameraState: StateFlow<CameraState> = _cameraState.asStateFlow()
    
    fun capturePhoto(outputFileOptions: ImageCapture.OutputFileOptions) {
        viewModelScope.launch {
            _cameraState.value = CameraState.Capturing
            // 拍照邏輯
        }
    }
}
```

**使用流程**：
1. 用戶進入拍照界面
2. 預覽相機畫面
3. 點擊拍照按鈕
4. 顯示拍照結果預覽
5. 確認或重新拍照
6. 進入梯形校正流程

### **梯形校正功能 (Perspective Correction)**

**核心文件**：
- `ui/correction/CorrectionScreen.kt` - 校正界面
- `ui/correction/CorrectionViewModel.kt` - 校正邏輯
- `utils/OpenCVHelper.kt` - OpenCV 工具類

**主要功能**：
- ✅ 自動邊界檢測
- ✅ 手動調整四個角點
- ✅ 透視變換校正
- ✅ 實時預覽效果
- ✅ 校正結果保存

**技術實現**：
```kotlin
object OpenCVHelper {
    fun detectDocumentEdges(bitmap: Bitmap): List<Point>? {
        // 使用 OpenCV 檢測文檔邊界
        val mat = Mat()
        Utils.bitmapToMat(bitmap, mat)
        
        // 邊界檢測算法
        val edges = detectEdges(mat)
        return findLargestContour(edges)
    }
    
    fun perspectiveTransform(
        bitmap: Bitmap, 
        corners: List<Point>
    ): Bitmap? {
        // 透視變換
        val srcPoints = MatOfPoint2f(*corners.toTypedArray())
        val dstPoints = MatOfPoint2f(/* 目標矩形角點 */)
        
        val transformMatrix = Imgproc.getPerspectiveTransform(srcPoints, dstPoints)
        // 應用變換
    }
}
```

**使用流程**：
1. 接收拍照結果
2. 自動檢測文檔邊界
3. 顯示檢測結果供用戶調整
4. 用戶可手動調整四個角點
5. 實時預覽校正效果
6. 確認校正並保存結果
7. 進入裁切流程

### **圖像裁切功能 (Image Cropping)**

**核心文件**：
- `ui/crop/CropScreen.kt` - 裁切界面
- `ui/crop/CropViewModel.kt` - 裁切邏輯
- `ui/crop/components/CropOverlay.kt` - 裁切覆蓋層

**主要功能**：
- ✅ 自由裁切框選擇
- ✅ 固定比例裁切
- ✅ 裁切框拖拽調整
- ✅ 縮放和平移支援
- ✅ 裁切預覽

**技術實現**：
```kotlin
@Composable
fun CropOverlay(
    imageSize: Size,
    cropRect: Rect,
    onCropRectChange: (Rect) -> Unit
) {
    Canvas(modifier = Modifier.fillMaxSize()) {
        // 繪製裁切框
        drawRect(
            color = Color.White,
            topLeft = cropRect.topLeft,
            size = cropRect.size,
            style = Stroke(width = 2.dp.toPx())
        )
        
        // 繪製角點控制器
        drawCropHandles(cropRect)
    }
}
```

## 📚 **錯題庫系統模組**

### **卡組管理功能 (Deck Management)**

**核心文件**：
- `ui/deck/DeckListScreen.kt` - 卡組列表界面
- `ui/deck/DeckDetailScreen.kt` - 卡組詳情界面
- `ui/deck/DeckViewModel.kt` - 卡組邏輯
- `data/manager/DeckDataManager.kt` - 卡組數據管理

**主要功能**：
- ✅ 創建新卡組
- ✅ 編輯卡組資訊
- ✅ 刪除卡組
- ✅ 卡組列表顯示
- ✅ 卡組統計資訊
- ✅ 卡組圖標和顏色

**數據模型**：
```kotlin
data class StudyDeck(
    val id: String,
    val name: String,
    val description: String = "",
    val icon: String = "📚",
    val color: String = "#6B7280",
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val cardCount: Int = 0,
    val studiedCount: Int = 0
)
```

**使用流程**：
1. 顯示所有卡組列表
2. 用戶可創建新卡組
3. 設置卡組名稱、描述、圖標
4. 進入卡組查看卡片
5. 編輯或刪除卡組

### **卡片管理功能 (Card Management)**

**核心文件**：
- `ui/card/CardListScreen.kt` - 卡片列表界面
- `ui/card/CardEditScreen.kt` - 卡片編輯界面
- `ui/card/CardViewModel.kt` - 卡片邏輯

**主要功能**：
- ✅ 創建新卡片
- ✅ 編輯卡片內容
- ✅ 刪除卡片
- ✅ 卡片列表顯示
- ✅ 圖文混合內容
- ✅ 標籤和難度管理

**數據模型**：
```kotlin
data class StudyCard(
    val id: String,
    val deckId: String,
    val question: String,
    val answer: String,
    val tags: List<String> = emptyList(),
    val difficulty: CardDifficulty = CardDifficulty.NORMAL,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val studiedCount: Int = 0,
    val correctCount: Int = 0,
    val lastStudiedAt: Long? = null
)

enum class CardDifficulty {
    EASY, NORMAL, HARD, VERY_HARD
}
```

**圖文混合內容格式**：
```json
[
  {
    "type": "text",
    "content": "解方程式 x + 5 = 12"
  },
  {
    "type": "image",
    "content": "equation_image.jpg"
  }
]
```

### **學習模式功能 (Study Mode)**

**核心文件**：
- `ui/study/StudyScreen.kt` - 學習界面
- `ui/study/StudyViewModel.kt` - 學習邏輯
- `ui/study/components/StudyCard.kt` - 學習卡片組件

**主要功能**：
- ✅ 卡片翻轉學習
- ✅ 難度評估
- ✅ 學習進度追蹤
- ✅ 複習提醒
- ✅ 學習統計

**學習算法**：
```kotlin
class StudyAlgorithm {
    fun getNextCard(deck: StudyDeck): StudyCard? {
        // 間隔重複算法 (Spaced Repetition)
        return deck.cards
            .filter { shouldReview(it) }
            .sortedBy { it.nextReviewDate }
            .firstOrNull()
    }
    
    private fun shouldReview(card: StudyCard): Boolean {
        val now = System.currentTimeMillis()
        return card.nextReviewDate <= now
    }
}
```

## 📥 **批量匯入功能模組**

### **CSV 匯入功能**

**核心文件**：
- `utils/BatchImportManager.kt` - 批量匯入管理
- `ui/import/ImportScreen.kt` - 匯入界面

**支援格式**：
```csv
題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
解方程式 x + 5 = 12,,x = 7,,一元一次方程式,簡單,基本移項法則
,complex_equation.jpg,,solution.jpg,二次方程式,困難,配方法求解
```

**匯入規則**：
- ✅ 只需要題目文字 OR 題目圖片（至少其中一個）
- ✅ 題目ID可選（系統自動生成）
- ✅ 答案可選（可以為空等待AI生成）
- ✅ 其他欄位全部可選

### **JSON 匯入功能**

**支援格式**：
```json
{
  "deckName": "數學題庫",
  "questions": [
    {
      "id": "Q001",
      "questionText": "解方程式 x + 5 = 12",
      "questionImage": "",
      "answer": "x = 7",
      "answerImage": "",
      "tags": ["一元一次方程式"],
      "difficulty": "EASY",
      "explanation": "基本移項法則"
    }
  ]
}
```

## 🖼️ **圖像存儲管理模組**

### **圖像存儲功能**

**核心文件**：
- `utils/ImageStorageManager.kt` - 圖像存儲管理

**主要功能**：
- ✅ 圖像壓縮和優化
- ✅ 統一的存儲路徑管理
- ✅ 圖像文件命名規則
- ✅ 存儲空間管理
- ✅ 圖像緩存機制

**技術實現**：
```kotlin
class ImageStorageManager(private val context: Context) {
    
    fun saveImage(bitmap: Bitmap): String? {
        val fileName = generateFileName()
        val file = File(getImagesDir(), fileName)
        
        return try {
            file.outputStream().use { out ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 85, out)
            }
            fileName
        } catch (e: Exception) {
            null
        }
    }
    
    private fun generateFileName(): String {
        return "img_${System.currentTimeMillis()}_${Random.nextInt(1000)}.jpg"
    }
}
```

## 🔧 **工具類模組**

### **OpenCV 工具類**
- 初始化 OpenCV
- 圖像處理算法
- 邊界檢測
- 透視變換

### **相機工具類**
- CameraX 配置
- 拍照參數設置
- 圖像質量控制

### **文件工具類**
- 文件操作
- 路徑管理
- 權限檢查

這份功能文檔詳細說明了每個模組的具體實現和使用方式，為開發者提供了完整的功能指南。
