# 卡片檢視問題診斷指南

## 問題描述

用戶報告：點擊題庫內卡片後，無法檢視卡片，直接跳回錯題庫畫面。

## 可能原因分析

### 1. Intent數據傳遞問題
- **卡片數據為null**：`intent.getParcelableExtra(EXTRA_CARD)`返回null
- **卡組ID為null**：`intent.getStringExtra(EXTRA_DECK_ID)`返回null
- **Parcelable序列化問題**：StudyCard序列化/反序列化失敗

### 2. Activity生命週期問題
- **onCreate中的異常**：初始化過程中發生未捕獲的異常
- **佈局載入問題**：activity_card_view.xml載入失敗
- **資源缺失**：必要的資源文件不存在

### 3. 權限或配置問題
- **AndroidManifest配置**：Activity未正確註冊
- **主題問題**：Activity主題配置錯誤
- **記憶體不足**：設備記憶體不足導致Activity無法啟動

## 診斷步驟

### 1. 檢查日誌輸出

現在已添加詳細的調試日誌，請檢查以下日誌：

#### DeckDetailActivity日誌
```
D/DeckDetail: 準備啟動CardViewActivity
D/DeckDetail: 卡片ID: [card_id]
D/DeckDetail: 卡組ID: [deck_id]
D/DeckDetail: Intent已創建，準備啟動Activity
D/DeckDetail: startActivity已調用
```

#### CardViewActivity日誌
```
D/CardView: onCreate 開始
D/CardView: 嘗試獲取卡片數據...
D/CardView: 成功獲取卡片數據: [card_id]
D/CardView: 成功獲取卡組ID: [deck_id]
D/CardView: 開始設置滑動行為
D/CardView: 滑動行為優化設置完成
D/CardView: 開始載入卡片內容
D/CardView: 題目內容: [content_preview]...
D/CardView: 題目內容載入成功
D/CardView: 卡片內容載入完成
D/CardView: onCreate 完成
```

### 2. 錯誤日誌檢查

如果出現以下錯誤日誌，表示對應的問題：

#### 數據傳遞問題
```
E/CardView: 無法獲取卡片數據，Activity將結束
E/CardView: 無法獲取卡組ID，Activity將結束
```

#### 內容載入問題
```
E/CardView: 載入題目內容失敗
E/CardView: 降級載入也失敗
E/CardView: 設置滑動行為失敗
```

### 3. 系統日誌檢查

檢查系統級別的錯誤：
```bash
adb logcat | grep -E "(CardView|DeckDetail|FATAL|AndroidRuntime)"
```

## 修復方案

### 1. 數據傳遞修復

如果是數據傳遞問題，檢查以下代碼：

```kotlin
// DeckDetailActivity.viewCard()
private fun viewCard(card: StudyCard) {
    val intent = Intent(this, CardViewActivity::class.java)
    intent.putExtra(CardViewActivity.EXTRA_CARD, card)
    intent.putExtra(CardViewActivity.EXTRA_DECK_ID, deckId)
    startActivity(intent)
}
```

確保：
- `card`不為null
- `deckId`不為null或空字串
- `StudyCard`正確實現Parcelable

### 2. 異常處理加強

已在關鍵位置添加try-catch：

```kotlin
// onCreate中的錯誤處理
card = intent.getParcelableExtra(EXTRA_CARD) ?: run {
    android.util.Log.e("CardView", "無法獲取卡片數據，Activity將結束")
    finish()
    return
}

// 內容載入的錯誤處理
try {
    binding.editContent.setRichContent(card.question)
} catch (e: Exception) {
    android.util.Log.e("CardView", "載入題目內容失敗", e)
    binding.editContent.setText(card.question)
}
```

### 3. 佈局問題修復

確保activity_card_view.xml中的所有ID正確：
- `scroll_content`
- `edit_content`
- `content_container`
- `fab_edit`

### 4. 記憶體問題處理

如果是記憶體問題，可以：
- 重啟應用程式
- 清理應用程式快取
- 重啟設備

## 測試步驟

### 1. 基本功能測試
1. 打開錯題庫應用程式
2. 進入任意卡組
3. 點擊任意卡片
4. 觀察是否正常進入卡片檢視

### 2. 日誌檢查測試
1. 連接設備到電腦
2. 運行 `adb logcat | grep CardView`
3. 執行上述基本功能測試
4. 檢查日誌輸出

### 3. 不同卡片測試
- 測試包含圖片的卡片
- 測試純文字卡片
- 測試長內容卡片
- 測試新創建的卡片

### 4. 不同卡組測試
- 測試不同的卡組
- 測試匯入的卡組
- 測試手動創建的卡組

## 常見解決方案

### 1. 應用程式重啟
最簡單的解決方案：
1. 完全關閉應用程式
2. 重新啟動應用程式
3. 重新測試功能

### 2. 快取清理
如果重啟無效：
1. 進入設定 > 應用程式
2. 找到錯題庫應用程式
3. 點擊「儲存空間」
4. 點擊「清除快取」

### 3. 數據重置
如果問題持續：
1. 備份重要數據
2. 清除應用程式數據
3. 重新設置應用程式

### 4. 重新安裝
最後手段：
1. 卸載應用程式
2. 重新安裝最新版本
3. 恢復數據（如果有備份）

## 預防措施

### 1. 數據驗證
在啟動Activity前驗證數據：
```kotlin
private fun viewCard(card: StudyCard) {
    if (card.id.isEmpty() || deckId.isEmpty()) {
        Toast.makeText(this, "卡片數據錯誤", Toast.LENGTH_SHORT).show()
        return
    }
    // 正常啟動邏輯...
}
```

### 2. 錯誤回饋
提供用戶友好的錯誤信息：
```kotlin
card = intent.getParcelableExtra(EXTRA_CARD) ?: run {
    Toast.makeText(this, "無法載入卡片，請重試", Toast.LENGTH_LONG).show()
    finish()
    return
}
```

### 3. 降級處理
提供備用方案：
```kotlin
try {
    binding.editContent.setRichContent(card.question)
} catch (e: Exception) {
    // 使用簡單文字顯示
    binding.editContent.setText(card.question)
}
```

## 開發者調試

### 1. 啟用詳細日誌
在開發版本中啟用更多日誌：
```kotlin
if (BuildConfig.DEBUG) {
    android.util.Log.v("CardView", "詳細調試信息")
}
```

### 2. 斷點調試
在關鍵位置設置斷點：
- `onCreate()`開始
- 數據獲取後
- 佈局設置後
- 內容載入後

### 3. 單元測試
為關鍵功能編寫測試：
- Intent數據傳遞測試
- Parcelable序列化測試
- Activity啟動測試

這個診斷指南應該能幫助快速定位和解決卡片檢視問題！
