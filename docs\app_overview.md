# 錯題庫應用程式 - 技術概覽

## 📱 **應用程式簡介**

**應用程式名稱**：錯題庫 (Error Analysis App)  
**主要功能**：拍照、梯形校正、裁切、錯題庫管理  
**目標平台**：Android (僅支援 arm64-v8a 架構)  
**開發語言**：Kotlin  
**最低 Android 版本**：API 24 (Android 7.0)  
**Java 版本**：21  

## 🎯 **核心功能模組**

### 1. **拍照與圖像處理**
- **拍照功能**：使用 CameraX 進行拍照
- **梯形校正**：使用 OpenCV 進行透視變換
- **圖像裁切**：自定義裁切工具
- **圖像存儲**：統一的圖像管理系統

### 2. **錯題庫系統**
- **卡組管理**：創建、編輯、刪除卡組
- **卡片管理**：題目、答案、標籤、難度管理
- **批量匯入**：支援 CSV 和 JSON 格式
- **學習模式**：複習和練習功能

### 3. **數據管理**
- **本地存儲**：SharedPreferences + JSON 文件
- **圖像存儲**：內部存儲目錄管理
- **數據同步**：實時數據更新

## 🏗️ **技術架構**

### **架構模式**
- **MVVM 模式**：Model-View-ViewModel
- **Repository 模式**：數據訪問層抽象
- **單例模式**：數據管理器

### **主要技術棧**
- **UI 框架**：Android Jetpack Compose
- **相機**：CameraX
- **圖像處理**：OpenCV 4.9.0
- **異步處理**：Kotlin Coroutines
- **依賴注入**：手動依賴注入
- **數據序列化**：JSON (org.json)

## 📁 **項目結構**

```
app/src/main/java/com/erroranalysis/app/
├── ui/                          # UI 層
│   ├── camera/                  # 拍照相關 UI
│   ├── crop/                    # 裁切相關 UI
│   ├── deck/                    # 卡組相關 UI
│   ├── card/                    # 卡片相關 UI
│   ├── import/                  # 匯入相關 UI
│   └── components/              # 共用 UI 組件
├── data/                        # 數據層
│   ├── model/                   # 數據模型
│   ├── repository/              # 數據倉庫
│   └── manager/                 # 數據管理器
├── utils/                       # 工具類
│   ├── ImageStorageManager.kt   # 圖像存儲管理
│   ├── BatchImportManager.kt    # 批量匯入管理
│   ├── OpenCVHelper.kt          # OpenCV 工具
│   └── CameraHelper.kt          # 相機工具
├── viewmodel/                   # ViewModel 層
└── MainActivity.kt              # 主活動
```

## 🔧 **核心組件說明**

### **數據模型**
- `StudyCard`：學習卡片模型
- `StudyDeck`：卡組模型
- `CardDifficulty`：難度枚舉
- `ImportResult`：匯入結果模型

### **管理器**
- `DeckDataManager`：卡組數據管理
- `ImageStorageManager`：圖像存儲管理
- `BatchImportManager`：批量匯入管理

### **工具類**
- `OpenCVHelper`：OpenCV 初始化和工具
- `CameraHelper`：相機相關工具
- `FileUtils`：文件操作工具

## 🎨 **UI 設計原則**

### **設計風格**
- **Material Design 3**：遵循 Google 設計規範
- **響應式設計**：適配不同螢幕尺寸
- **無障礙設計**：支援輔助功能

### **色彩系統**
- **主色調**：藍色系 (#4A90E2)
- **輔助色**：根據學科分類
- **狀態色**：成功(綠)、警告(橙)、錯誤(紅)

### **字體系統**
- **主字體**：系統默認字體
- **大小層級**：標題、正文、說明文字
- **權重**：粗體、常規、細體

## 📊 **數據流架構**

### **數據流向**
```
UI Layer (Compose) 
    ↕
ViewModel Layer 
    ↕
Repository Layer 
    ↕
Data Manager Layer 
    ↕
Local Storage (SharedPreferences + Files)
```

### **狀態管理**
- **UI 狀態**：Compose State
- **業務狀態**：ViewModel + StateFlow
- **數據狀態**：Repository + Flow

## 🔒 **安全與隱私**

### **數據安全**
- **本地存儲**：所有數據存儲在應用私有目錄
- **圖像安全**：圖像文件加密存儲
- **數據備份**：支援本地備份和恢復

### **隱私保護**
- **無網絡權限**：應用完全離線運行
- **無外部存儲**：不訪問外部存儲
- **無位置信息**：不收集位置數據

## 🚀 **性能優化**

### **圖像處理優化**
- **內存管理**：及時回收 Bitmap
- **圖像壓縮**：智能壓縮算法
- **異步處理**：後台處理圖像

### **UI 性能優化**
- **懶加載**：列表項目懶加載
- **狀態保存**：配置變更時保存狀態
- **動畫優化**：流暢的過渡動畫

## 🧪 **測試策略**

### **測試類型**
- **單元測試**：業務邏輯測試
- **UI 測試**：用戶界面測試
- **集成測試**：模組間集成測試

### **測試工具**
- **JUnit**：單元測試框架
- **Espresso**：UI 測試框架
- **Mockito**：模擬對象框架

## 📦 **構建與部署**

### **構建配置**
- **Gradle**：構建工具
- **ProGuard**：代碼混淆
- **APK 優化**：僅包含 arm64-v8a 架構

### **版本管理**
- **語義化版本**：主版本.次版本.修訂版本
- **構建變體**：Debug、Release
- **簽名配置**：發布簽名

## 🔮 **未來規劃**

### **功能擴展**
- **AI 答案生成**：集成 AI 模型
- **雲端同步**：可選的雲端備份
- **多語言支援**：國際化支援

### **技術升級**
- **Compose 多平台**：支援其他平台
- **模組化架構**：功能模組化
- **性能監控**：應用性能監控

## 📞 **技術支援**

### **開發環境要求**
- **Android Studio**：最新穩定版
- **JDK**：21 或更高版本
- **Android SDK**：API 34
- **Gradle**：8.11.1

### **依賴管理**
- **OpenCV**：4.9.0
- **CameraX**：1.3.0
- **Compose BOM**：最新版本
- **Kotlin**：1.9.0

這份文檔提供了應用程式的整體技術概覽，後續的詳細文檔將深入每個模組的具體實現。
