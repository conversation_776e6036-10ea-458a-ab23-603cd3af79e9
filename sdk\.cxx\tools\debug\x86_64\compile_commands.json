[{"directory": "C:/augment-projects/HomeWork/android/ErrorAnalysisApp/sdk/.cxx/Debug/5r5e101e/x86_64", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=x86_64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -o CMakeFiles\\opencv_jni_shared.dir\\dummy.cpp.o -c C:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\sdk\\libcxx_helper\\dummy.cpp", "file": "C:\\augment-projects\\HomeWork\\android\\ErrorAnalysisApp\\sdk\\libcxx_helper\\dummy.cpp"}]