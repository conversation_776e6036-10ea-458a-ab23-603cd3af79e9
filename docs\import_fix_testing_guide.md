# 匯入修復測試指南

## 🧪 測試重複匯入修復

### 測試步驟

1. **準備測試檔案**
   ```
   測試檔案：數學-基礎代數.csv
   內容：包含幾個簡單的數學題目
   ```

2. **第一次匯入**
   - 開啟批次匯入功能
   - 選擇`數學-基礎代數.csv`
   - 確認匯入成功
   - 記錄卡組名稱：`數學 - 基礎代數`

3. **第二次匯入（測試重複檢查）**
   - 再次選擇相同的`數學-基礎代數.csv`檔案
   - 觀察匯入結果

### 預期結果

**修復前**：
- ❌ 會創建重複的卡組
- ❌ 兩個同名卡組存在

**修復後**：
- ✅ 自動創建新卡組名稱：`數學 - 基礎代數 (20241219_1430)`
- ✅ 匯入結果顯示：`ℹ️ 因同名卡組已存在，已創建新卡組：數學 - 基礎代數 (20241219_1430)`
- ✅ 原有卡組保持不變

### 驗證方法

1. **檢查卡組列表**
   - 進入錯題庫主頁
   - 確認有兩個不同名稱的卡組
   - 原卡組：`數學 - 基礎代數`
   - 新卡組：`數學 - 基礎代數 (時間戳)`

2. **檢查日誌**
   ```bash
   adb logcat | grep "BatchImportManager"
   ```
   
   應該看到：
   ```
   ⚠️ 發現同名卡組：數學 - 基礎代數
   ✅ 創建新卡組避免重複：數學 - 基礎代數 (20241219_1430)
   ```

## 🖼️ 測試圖片匯入修復

### 準備測試檔案

1. **創建測試ZIP檔案**
   ```
   test_images.zip
   ├── questions.csv
   └── images/
       ├── math_eq1.jpg
       ├── math_eq2.png
       └── diagram.jpg
   ```

2. **CSV檔案內容**
   ```csv
   題目ID,題目文字,題目圖片,答案,標籤,難度
   Q001,解這個方程式,math_eq1.jpg,x = 5,代數,簡單
   Q002,,math_eq2.png,y = 3,代數,普通
   Q003,分析這個圖表,diagram.jpg,見圖解,統計,困難
   ```

### 測試案例

#### 案例1：正常圖片匯入
- **題目**：Q001（有文字 + 有圖片）
- **預期**：成功匯入，顯示文字和圖片

#### 案例2：只有圖片的題目
- **題目**：Q002（無文字 + 有圖片）
- **預期**：成功匯入，只顯示圖片

#### 案例3：圖片檔案不存在
- **修改CSV**：將`math_eq1.jpg`改為`missing.jpg`
- **預期**：顯示錯誤但不中斷匯入

### 驗證方法

1. **檢查匯入結果**
   - 確認所有題目都成功匯入
   - 檢查圖片是否正確顯示

2. **檢查詳細日誌**
   ```bash
   adb logcat | grep "處理圖片\|成功添加圖片\|圖片檔案不存在"
   ```
   
   應該看到：
   ```
   處理圖片：math_eq1.jpg
   圖片目錄：/path/to/images
   圖片完整路徑：/path/to/images/math_eq1.jpg
   圖片檔案是否存在：true
   ✅ 成功添加圖片：uuid.jpg
   ```

3. **檢查卡片內容**
   - 進入卡組查看卡片
   - 確認圖片正確顯示
   - 檢查只有圖片的卡片是否正常

## 🔍 故障排除

### 重複匯入問題

**問題**：仍然出現重複卡組
**檢查**：
1. 確認應用程式已更新到最新版本
2. 檢查日誌是否有重複檢查的記錄
3. 清除應用程式快取後重試

**問題**：時間戳格式不正確
**檢查**：
1. 確認系統時間設定正確
2. 檢查日誌中的時間戳格式

### 圖片匯入問題

**問題**：圖片無法顯示
**檢查**：
1. 確認ZIP檔案結構正確
2. 檢查圖片檔案名稱是否與CSV一致
3. 確認圖片格式支援（JPG、PNG）

**問題**：只有圖片的題目匯入失敗
**檢查**：
1. 確認CSV中題目文字欄位為空
2. 檢查圖片檔案是否存在
3. 查看詳細日誌信息

## 📋 測試檢查清單

### 重複匯入測試
- [ ] 第一次匯入成功
- [ ] 第二次匯入創建新卡組名稱
- [ ] 匯入結果顯示重複通知
- [ ] 原卡組保持不變
- [ ] 新卡組名稱包含時間戳

### 圖片匯入測試
- [ ] 有文字+有圖片的題目正常匯入
- [ ] 只有圖片的題目正常匯入
- [ ] 圖片檔案不存在時有適當錯誤處理
- [ ] 圖片在卡片中正確顯示
- [ ] 日誌記錄詳細的圖片處理信息

### 整體功能測試
- [ ] 應用程式編譯成功
- [ ] 批次匯入功能正常運作
- [ ] 錯誤處理機制正常
- [ ] 用戶界面顯示正確信息
- [ ] 日誌記錄完整且有用

## 🎯 成功標準

### 重複匯入修復
- ✅ 不再產生完全相同名稱的重複卡組
- ✅ 自動生成有意義的新卡組名稱
- ✅ 用戶收到清晰的通知信息
- ✅ 原有資料保持完整

### 圖片匯入修復
- ✅ 只有圖片的題目能正確匯入
- ✅ 圖片處理錯誤有詳細日誌
- ✅ 提供適當的降級處理
- ✅ 圖片在界面中正確顯示

## 📞 回報問題

如果測試中發現問題，請提供：

1. **重複匯入問題**：
   - 匯入的檔案名稱
   - 現有卡組列表截圖
   - 匯入結果截圖
   - logcat日誌

2. **圖片匯入問題**：
   - ZIP檔案結構
   - CSV檔案內容
   - 圖片檔案列表
   - 匯入結果和錯誤信息
   - 詳細的logcat日誌

這個測試指南將幫助確保修復功能正常運作，並提供問題診斷的方法。
