{"archive": {}, "artifacts": [{"path": "libopencv_jni_shared.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 6, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}], "language": "CXX", "sourceIndexes": [0], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "opencv_jni_shared::@6890427a1f51a3e7e1df", "name": "opencv_jni_shared", "nameOnDisk": "libopencv_jni_shared.a", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "dummy.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}