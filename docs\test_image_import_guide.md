# 圖片匯入測試指南

## 🧪 測試圖片匯入修復

### 準備測試檔案

1. **創建測試ZIP檔案**
   ```
   test_image_import.zip
   ├── test_image_import.csv
   └── images/
       ├── math_eq1.jpg     (任何JPG圖片)
       ├── math_eq2.png     (任何PNG圖片)
       └── diagram.jpg      (任何JPG圖片)
   ```

2. **使用提供的CSV檔案**
   - 檔案位置：`docs/test_image_import.csv`
   - 包含5個測試案例：
     - Q001：有文字 + 有圖片
     - Q002：無文字 + 有圖片（只有圖片）
     - Q003：有文字 + 有圖片
     - Q004：有文字 + 無圖片（純文字）
     - Q005：無文字 + 圖片不存在（測試錯誤處理）

### 測試步驟

#### 步驟1：準備圖片檔案
1. 找3張任意圖片（JPG或PNG格式）
2. 重新命名為：
   - `math_eq1.jpg`
   - `math_eq2.png`
   - `diagram.jpg`
3. 放入`images/`目錄

#### 步驟2：創建ZIP檔案
1. 將`test_image_import.csv`和`images/`目錄打包成ZIP
2. 命名為`test_image_import.zip`

#### 步驟3：執行匯入測試
1. 開啟應用程式的批次匯入功能
2. 選擇`test_image_import.zip`檔案
3. 執行匯入

#### 步驟4：檢查匯入結果
1. 確認匯入成功
2. 進入新創建的卡組
3. 檢查每張卡片的內容

### 預期結果

#### Q001：有文字 + 有圖片
- ✅ 應該顯示：文字"解這個方程式" + 圖片
- ✅ 圖片應該正確顯示

#### Q002：無文字 + 有圖片（只有圖片）
- ✅ 應該顯示：只有圖片，沒有文字
- ✅ 這是修復的重點：之前會完全消失

#### Q003：有文字 + 有圖片
- ✅ 應該顯示：文字"分析這個圖表" + 圖片
- ✅ 圖片應該正確顯示

#### Q004：有文字 + 無圖片（純文字）
- ✅ 應該顯示：只有文字"這是純文字題目"
- ✅ 沒有圖片，這是正常的

#### Q005：無文字 + 圖片不存在（測試錯誤處理）
- ✅ 應該顯示：佔位符文字"[圖片檔案不存在: missing_image.jpg]"
- ✅ 這是修復的重點：之前會完全消失

### 驗證方法

#### 1. 檢查卡片內容
- 進入卡組，逐一查看每張卡片
- 確認內容符合預期結果

#### 2. 檢查日誌
```bash
adb logcat | grep "BatchImportManager"
```

應該看到類似日誌：
```
處理圖片：math_eq1.jpg
✅ 成功添加圖片：uuid.jpg
處理圖片：math_eq2.png
✅ 成功添加圖片：uuid.jpg
處理圖片：diagram.jpg
✅ 成功添加圖片：uuid.jpg
處理圖片：missing_image.jpg
⚠️ 圖片檔案不存在：missing_image.jpg
⚠️ 添加圖片佔位符：[圖片檔案不存在: missing_image.jpg]
```

#### 3. 檢查匯入統計
匯入結果應該顯示：
- 成功匯入：5/5 個題目
- 可能有警告信息關於missing_image.jpg

## 🔍 故障排除

### 問題：Q002（只有圖片）沒有顯示
**檢查**：
1. 確認使用的是修復後的版本
2. 檢查ZIP檔案結構是否正確
3. 確認圖片檔案存在且名稱正確

### 問題：圖片顯示為佔位符文字
**可能原因**：
1. 圖片檔案不存在於images目錄中
2. 圖片檔案名稱與CSV不匹配
3. 圖片檔案損壞或格式不支援

**解決方法**：
1. 檢查images目錄內容
2. 確認檔案名稱大小寫匹配
3. 嘗試使用其他圖片檔案

### 問題：匯入失敗
**檢查**：
1. ZIP檔案結構是否正確
2. CSV檔案格式是否正確
3. 查看詳細錯誤信息

## 📋 測試檢查清單

### 基本功能測試
- [ ] ZIP檔案可以正確匯入
- [ ] 有文字+有圖片的題目正常顯示
- [ ] 只有圖片的題目正常顯示（Q002）
- [ ] 純文字題目正常顯示
- [ ] 圖片不存在時顯示佔位符（Q005）

### 圖片處理測試
- [ ] JPG圖片正確匯入
- [ ] PNG圖片正確匯入
- [ ] 圖片在卡片中正確顯示
- [ ] 圖片檔案不存在時有適當處理

### 日誌記錄測試
- [ ] 圖片處理過程有詳細日誌
- [ ] 成功匯入的圖片有記錄
- [ ] 失敗的圖片有錯誤記錄
- [ ] 佔位符添加有記錄

### 錯誤處理測試
- [ ] 圖片檔案不存在時不會導致題目消失
- [ ] 圖片解碼失敗時有適當處理
- [ ] 圖片保存失敗時有適當處理
- [ ] 用戶收到清晰的錯誤信息

## 🎯 成功標準

### 修復驗證
- ✅ Q002（只有圖片）能正確顯示
- ✅ Q005（圖片不存在）顯示佔位符而不是消失
- ✅ 所有5個測試案例都能成功匯入
- ✅ 圖片處理有詳細的日誌記錄

### 用戶體驗
- ✅ 不會因為圖片問題導致題目丟失
- ✅ 圖片匯入失敗時有明確提示
- ✅ 匯入過程穩定可靠
- ✅ 錯誤信息有助於問題診斷

## 📞 回報測試結果

如果測試中發現問題，請提供：

1. **測試環境**：
   - 應用程式版本
   - Android版本
   - 測試檔案結構

2. **問題描述**：
   - 哪個測試案例失敗
   - 實際結果vs預期結果
   - 錯誤信息截圖

3. **日誌信息**：
   ```bash
   adb logcat | grep "BatchImportManager" > import_test_log.txt
   ```

4. **測試檔案**：
   - 使用的ZIP檔案
   - CSV檔案內容
   - 圖片檔案列表

這個測試指南將幫助驗證圖片匯入修復是否正常運作，特別是只有圖片的題目是否能正確匯入。
