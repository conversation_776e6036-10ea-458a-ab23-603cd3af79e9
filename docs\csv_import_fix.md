# CSV匯入功能修復說明

## 修復內容

### 1. 清理舊代碼
✅ **已刪除的檔案**：
- `CardViewActivity.kt` - 舊的卡片檢視Activity
- `TestCardViewActivity.kt` - 測試用Activity
- `activity_card_view.xml` - 舊的佈局檔案
- AndroidManifest中的舊Activity註冊

✅ **保留的檔案**：
- `CardViewerActivity.kt` - 全新的卡片檢視Activity
- `activity_card_viewer.xml` - 新的佈局檔案

### 2. CSV匯入功能改進

#### 界面更新
✅ **說明文字更新**：
```
📋 支援的檔案格式：
• CSV檔案（推薦）- 直接匯入，檔名作為卡組名稱
• ZIP壓縮包（包含questions.json和images資料夾）
• JSON檔案（純文字題目）

📁 CSV檔案範例：
數學-代數基礎.csv
物理-力學基礎.csv
英文-基礎文法.csv

💡 推薦使用CSV格式，簡單易用
```

✅ **範例格式更新**：
- 按鈕文字：`📄 查看CSV格式範例`
- 顯示詳細的CSV格式說明和範例

#### 錯誤處理改進
✅ **多層次錯誤檢查**：
```kotlin
// 1. 檔案內容檢查
if (csvContent.isBlank()) {
    return ImportResult.Error("CSV檔案為空或無法讀取")
}

// 2. 行數檢查
if (lines.size < 2) {
    return ImportResult.Error("CSV檔案至少需要標題行和一行資料")
}

// 3. 標題行解析檢查
val headers = try {
    parseCsvLine(lines[0])
} catch (e: Exception) {
    return ImportResult.Error("CSV標題行格式錯誤：${e.message}")
}

// 4. 必要欄位檢查
val requiredFields = mapOf(
    "id" to listOf("題目ID", "ID", "id", "題目id"),
    "answer" to listOf("答案", "answer", "Answer")
)
```

✅ **詳細錯誤信息**：
```kotlin
if (missingFields.isNotEmpty()) {
    return ImportResult.Error(
        "CSV缺少必要欄位：${missingFields.joinToString(", ")}\n\n" +
        "實際欄位：${headers.joinToString(", ")}"
    )
}
```

✅ **異常類型識別**：
```kotlin
val errorMessage = when (e) {
    is java.io.FileNotFoundException -> "找不到檔案"
    is java.io.IOException -> "檔案讀取錯誤：${e.message}"
    is org.json.JSONException -> "資料格式錯誤：${e.message}"
    is java.lang.IllegalArgumentException -> "參數錯誤：${e.message}"
    else -> "未知錯誤：${e.message}"
}
```

#### 欄位支援改進
✅ **支援多種欄位名稱變體**：
- **ID欄位**：`題目ID`, `ID`, `id`, `題目id`
- **答案欄位**：`答案`, `answer`, `Answer`
- **題目欄位**：`題目文字`, `題目`
- **圖片欄位**：`題目圖片`, `圖片檔名`
- **標籤欄位**：`標籤`, `tag`
- **難度欄位**：`難度`, `difficulty`

## CSV格式規範

### 必要欄位
1. **題目ID** - 唯一識別碼
2. **答案** - 題目答案

### 可選欄位
3. **題目文字** - 題目內容
4. **題目圖片** - 圖片檔名
5. **標籤** - 分類標籤
6. **難度** - 簡單/普通/困難
7. **說明** - 解題說明

### 範例檔案
已提供範例檔案：`docs/數學-基礎代數.csv`

```csv
題目ID,題目文字,題目圖片,答案,標籤,難度,說明
Q001,計算 2 + 3 的值,,5,基礎數學,簡單,基本加法運算
Q002,解方程式 x + 5 = 12,,x = 7,代數,簡單,一元一次方程式
Q003,計算 3 × 4 的值,,12,基礎數學,簡單,基本乘法運算
```

## 使用指南

### 1. 準備CSV檔案
- 使用UTF-8編碼保存
- 第一行為標題行
- 至少包含「題目ID」和「答案」欄位
- 檔案名稱會成為卡組名稱

### 2. 匯入步驟
1. 打開錯題庫應用程式
2. 進入任意卡組或主頁面
3. 點擊「批次匯入」功能
4. 點擊「📁 選擇題庫檔案」
5. 選擇準備好的CSV檔案
6. 等待匯入完成

### 3. 錯誤排除
如果匯入失敗，檢查：
- 檔案編碼是否為UTF-8
- 是否包含必要欄位（題目ID、答案）
- CSV格式是否正確（逗號分隔）
- 檔案是否損壞

## 測試建議

### 1. 基本功能測試
- 使用提供的範例CSV檔案測試
- 測試不同的檔案名稱格式
- 測試包含中文的內容

### 2. 錯誤處理測試
- 測試空檔案
- 測試缺少必要欄位的檔案
- 測試格式錯誤的檔案
- 測試編碼錯誤的檔案

### 3. 邊界條件測試
- 測試只有標題行的檔案
- 測試欄位數量不匹配的檔案
- 測試包含特殊字符的內容

## 技術改進

### 1. 錯誤處理機制
- 多層次的錯誤檢查
- 詳細的錯誤信息提示
- 異常類型識別和分類

### 2. 用戶體驗改進
- 清晰的格式說明
- 詳細的範例展示
- 友好的錯誤提示

### 3. 兼容性提升
- 支援多種欄位名稱變體
- 支援不同的編碼格式
- 支援不同的CSV方言

這些改進應該能顯著提升CSV匯入功能的可靠性和用戶體驗！
