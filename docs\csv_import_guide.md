# CSV批次匯入功能指南

## 功能概述

現在支援直接使用CSV檔案進行批次匯入，無需先轉換為JSON格式。用戶只需準備CSV檔案和圖片資料夾，App就能直接產生卡片。

## CSV檔案格式

### 必要欄位
- **題目ID**：每個題目的唯一識別碼
- **答案**：題目的標準答案

### 可選欄位
- **題目文字**：題目的文字描述
- **題目圖片**：題目圖片的檔案名稱
- **答案圖片**：答案圖片的檔案名稱
- **標籤**：題目標籤（用逗號分隔）
- **難度**：題目難度（簡單/普通/困難/非常困難）
- **說明**：額外的解釋說明

### 範例CSV格式

```csv
題目ID,題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
Q001,計算 2+3 的值,math_001.jpg,5,,基礎數學,簡單,基本加法運算
Q002,解方程式 x²-5x+6=0,equation_001.png,"x=2 或 x=3",,代數,普通,因式分解法
Q003,,geometry_001.jpg,30°,angle_001.png,幾何,困難,使用三角函數
Q004,積分計算,integral_001.png,x²/2 + C,,微積分,非常困難,基本積分公式
```

### 欄位說明

#### 1. 題目ID
- **格式**：字串，建議使用Q001、Q002等格式
- **必要性**：必要
- **說明**：如果留空，系統會自動生成

#### 2. 題目文字
- **格式**：純文字
- **必要性**：可選
- **說明**：題目的文字描述，可以為空（純圖片題目）

#### 3. 題目圖片
- **格式**：圖片檔案名稱（含副檔名）
- **必要性**：可選
- **說明**：圖片檔案需放在images資料夾中

#### 4. 答案
- **格式**：純文字
- **必要性**：必要
- **說明**：題目的標準答案

#### 5. 答案圖片
- **格式**：圖片檔案名稱（含副檔名）
- **必要性**：可選
- **說明**：答案的圖片說明

#### 6. 標籤
- **格式**：用逗號分隔的標籤列表
- **必要性**：可選
- **範例**：`數學,代數,方程式` 或 `基礎,重要`

#### 7. 難度
- **格式**：文字
- **可選值**：
  - `簡單` 或 `EASY`
  - `普通` 或 `NORMAL`（預設）
  - `困難` 或 `HARD`
  - `非常困難` 或 `VERY_HARD`

#### 8. 說明
- **格式**：純文字
- **必要性**：可選
- **說明**：額外的解題說明或提示

## 使用流程

### 1. 準備CSV檔案
```
題庫資料夾/
├── questions.csv          # 題目資料
└── images/               # 圖片資料夾
    ├── math_001.jpg
    ├── equation_001.png
    ├── geometry_001.jpg
    └── ...
```

### 2. CSV檔案內容範例
```csv
題目ID,題目文字,題目圖片,答案,標籤,難度
Q001,什麼是2+2?,,"4",基礎數學,簡單
Q002,,math_problem.jpg,"x=5",代數,普通
Q003,計算積分∫x dx,integral.png,"x²/2 + C","微積分,積分",困難
```

### 3. 匯入步驟
1. 在App中選擇「批次匯入」
2. 選擇CSV檔案
3. 系統自動解析並創建卡組
4. 檢查匯入結果

## 技術特點

### 1. 智能欄位識別
系統會自動識別以下欄位名稱變體：
- **ID欄位**：`題目ID`、`ID`、`id`
- **題目欄位**：`題目文字`、`題目`、`question`
- **圖片欄位**：`題目圖片`、`圖片檔名`、`image`
- **答案欄位**：`答案`、`answer`
- **標籤欄位**：`標籤`、`tag`、`tags`
- **難度欄位**：`難度`、`difficulty`

### 2. CSV解析功能
- **引號處理**：支援引號包圍的欄位
- **逗號轉義**：正確處理欄位內的逗號
- **編碼支援**：支援UTF-8編碼的中文內容

### 3. 錯誤處理
- **欄位驗證**：檢查必要欄位是否存在
- **資料驗證**：驗證每行資料的完整性
- **錯誤報告**：提供詳細的錯誤信息

### 4. 圖片處理
- **自動載入**：從images資料夾自動載入圖片
- **格式支援**：支援JPG、PNG等常見格式
- **錯誤處理**：圖片不存在時的降級處理

## 優勢對比

### 改進前（JSON格式）
```
1. 用戶準備CSV檔案
2. 用戶使用Python腳本轉換為JSON
3. 用戶打包為ZIP檔案
4. App匯入ZIP檔案
```

### 改進後（直接CSV）
```
1. 用戶準備CSV檔案和圖片
2. App直接匯入CSV檔案
```

### 用戶體驗改進
- ✅ **簡化流程**：減少2個步驟
- ✅ **降低門檻**：不需要Python技能
- ✅ **即時匯入**：準備好就能匯入
- ✅ **錯誤友好**：清晰的錯誤提示

## 最佳實踐

### 1. CSV檔案準備
- 使用UTF-8編碼保存
- 第一行必須是標題行
- 避免在欄位內使用不必要的引號
- 確保必要欄位不為空

### 2. 圖片準備
- 圖片檔案名稱要與CSV中的名稱完全一致
- 建議使用有意義的檔案名稱
- 支援的格式：JPG、PNG、GIF、WEBP

### 3. 資料組織
- 題目ID使用統一格式（如Q001、Q002）
- 標籤使用一致的命名規則
- 難度設定要準確反映題目複雜度

### 4. 測試建議
- 先用少量資料測試匯入
- 檢查圖片是否正確顯示
- 驗證標籤和難度設定

## 錯誤排除

### 常見問題

#### 1. "CSV缺少必要欄位"
- **原因**：CSV檔案沒有「題目ID」或「答案」欄位
- **解決**：確保第一行包含這些欄位名稱

#### 2. "第X行欄位數量不匹配"
- **原因**：某行的欄位數量與標題行不一致
- **解決**：檢查該行是否有多餘或缺少的逗號

#### 3. "圖片檔案不存在"
- **原因**：CSV中指定的圖片檔案在images資料夾中找不到
- **解決**：檢查檔案名稱是否正確，包括副檔名

#### 4. "答案不能為空"
- **原因**：某個題目的答案欄位為空
- **解決**：為每個題目提供答案

### 除錯技巧
1. 使用文字編輯器檢查CSV格式
2. 確認檔案編碼為UTF-8
3. 檢查是否有隱藏字符
4. 逐行檢查資料完整性

這個改進讓批次匯入功能更加用戶友好，大幅降低了使用門檻！
