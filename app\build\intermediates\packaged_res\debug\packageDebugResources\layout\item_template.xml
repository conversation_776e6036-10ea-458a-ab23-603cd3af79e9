<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:gravity="center"
    android:orientation="vertical">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_template"
        android:layout_width="60dp"
        android:layout_height="80dp"
        android:clickable="true"
        android:focusable="true"
        app:cardCornerRadius="12dp"
        app:cardElevation="2dp"
        app:strokeColor="@android:color/transparent"
        app:strokeWidth="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="8dp">

            <TextView
                android:id="@+id/text_template_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="📘"
                android:textSize="24sp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <TextView
        android:id="@+id/text_template_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="數學"
        android:textSize="10sp" />

</LinearLayout>
