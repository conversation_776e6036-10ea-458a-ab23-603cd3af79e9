{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "opencv_jni_shared", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "opencv_jni_shared::@6890427a1f51a3e7e1df", "jsonFile": "target-opencv_jni_shared-Debug-580e2188caa0aa6e6c04.json", "name": "opencv_jni_shared", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/augment-projects/HomeWork/android/ErrorAnalysisApp/sdk/.cxx/Debug/5r5e101e/armeabi-v7a", "source": "C:/augment-projects/HomeWork/android/ErrorAnalysisApp/sdk/libcxx_helper"}, "version": {"major": 2, "minor": 3}}