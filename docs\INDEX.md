# 📚 錯題庫應用文檔索引

## 📋 文檔結構

### 🎯 主要文檔
- **[README.md](README.md)** - 項目概述和完整開發文檔
- **[TECHNICAL_DETAILS.md](TECHNICAL_DETAILS.md)** - 技術實現詳細文檔
- **[DEVELOPMENT_GUIDE.md](DEVELOPMENT_GUIDE.md)** - 開發指南和最佳實踐

### 📁 文檔目錄
```
docs/
├── INDEX.md                    # 文檔索引（本文件）
├── README.md                   # 主要項目文檔
├── TECHNICAL_DETAILS.md        # 技術實現細節
├── DEVELOPMENT_GUIDE.md        # 開發指南
└── api/                        # API文檔（未來擴展）
```

## 🚀 快速導航

### 對於新的Agent/開發者
1. **首先閱讀**: [README.md](README.md) - 了解項目整體架構
2. **技術細節**: [TECHNICAL_DETAILS.md](TECHNICAL_DETAILS.md) - 深入了解實現
3. **開發指南**: [DEVELOPMENT_GUIDE.md](DEVELOPMENT_GUIDE.md) - 開始開發

### 對於特定問題
- **圖片縮放問題** → [TECHNICAL_DETAILS.md#圖片縮放核心實現](TECHNICAL_DETAILS.md)
- **數據格式問題** → [README.md#數據模型](README.md)
- **UI組件問題** → [README.md#UI組件詳解](README.md)
- **調試問題** → [DEVELOPMENT_GUIDE.md#調試和測試](DEVELOPMENT_GUIDE.md)

## 🔍 關鍵信息快速查找

### 核心組件位置
```
RichTextEditText: app/src/main/java/com/erroranalysis/app/ui/widgets/RichTextEditText.kt
CardViewActivity: app/src/main/java/com/erroranalysis/app/ui/study/CardViewActivity.kt
CardEditActivity: app/src/main/java/com/erroranalysis/app/ui/study/CardEditActivity.kt
ImageStorageManager: app/src/main/java/com/erroranalysis/app/utils/ImageStorageManager.kt
```

### 關鍵技術實現
- **圖片縮放**: 簡單雙指距離計算，不依賴ScaleGestureDetector
- **圖文混合**: JSON格式存儲，SpannableString顯示
- **圖片存儲**: 應用私有目錄，UUID文件名
- **數據持久化**: Room數據庫

### 已知問題和解決方案
- **ScaleGestureDetector不穩定** → 使用簡單雙指縮放
- **卡片列表顯示JSON** → 實現getPlainTextPreview()
- **檢視模式衝突** → 創建專用CardViewActivity

## 📱 功能狀態

### ✅ 已完成
- [x] 卡組和卡片管理
- [x] 圖文混合編輯器
- [x] 圖片插入和縮放
- [x] 卡片檢視界面
- [x] 點擊切換題目/答案
- [x] 數據持久化

### 🔧 技術特色
- [x] 自定義RichTextEditText組件
- [x] 簡單可靠的雙指縮放
- [x] JSON格式圖文混合存儲
- [x] 只讀模式支援

## 🎯 重要提醒

### 對於新Agent
1. **核心組件**: RichTextEditText是整個應用的核心
2. **關鍵實現**: 簡單雙指縮放是最穩定的解決方案
3. **數據格式**: JSON格式的圖文混合內容是存儲核心
4. **用戶體驗**: CardViewActivity提供最佳檢視體驗

### 開發注意事項
- 圖片操作後必須調用invalidate()
- 多指觸控檢測要在onTouchEvent最前面
- 圖片文件使用UUID命名避免衝突
- 支援只讀模式通過setReadOnlyMode()

### 調試方法
- 使用Toast在手機上顯示調試信息
- 關鍵日誌標籤: "RichTextEditText", "CardView", "DeckDetail"
- 圖片縮放調試: 檢查多指檢測 → 圖片檢測 → 縮放邏輯

## 📞 聯繫信息

### 文檔維護
- 創建日期: 2024年
- 最後更新: 當前對話
- 維護者: AI Assistant

### 版本信息
- 應用版本: 1.0
- 文檔版本: 1.0
- Android目標SDK: 34

---

**這些文檔包含了錯題庫應用的完整開發信息，新的Agent可以基於這些文檔快速理解項目並繼續開發。**
