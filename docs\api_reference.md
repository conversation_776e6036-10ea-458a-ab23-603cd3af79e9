# API 參考和工具類文檔

## 🔧 **核心工具類 API**

### **ImageStorageManager**

**用途**：統一管理應用程式中的圖像存儲和處理

**位置**：`utils/ImageStorageManager.kt`

**主要方法**：

```kotlin
class ImageStorageManager(private val context: Context) {
    
    /**
     * 保存圖片到內部存儲
     * @param bitmap 要保存的圖片
     * @param quality 壓縮質量 (0-100)
     * @return 保存的文件名，失敗返回 null
     */
    fun saveImage(bitmap: Bitmap, quality: Int = 85): String?
    
    /**
     * 從文件名載入圖片
     * @param fileName 圖片文件名
     * @return Bitmap 對象，失敗返回 null
     */
    fun loadImage(fileName: String): Bitmap?
    
    /**
     * 刪除圖片文件
     * @param fileName 要刪除的文件名
     * @return 是否成功刪除
     */
    fun deleteImage(fileName: String): Boolean
    
    /**
     * 獲取圖片文件大小
     * @param fileName 圖片文件名
     * @return 文件大小（字節），文件不存在返回 -1
     */
    fun getImageSize(fileName: String): Long
    
    /**
     * 壓縮圖片
     * @param bitmap 原始圖片
     * @param maxWidth 最大寬度
     * @param maxHeight 最大高度
     * @return 壓縮後的圖片
     */
    fun compressImage(bitmap: Bitmap, maxWidth: Int, maxHeight: Int): Bitmap
    
    /**
     * 獲取圖片存儲目錄
     * @return 圖片存儲目錄 File 對象
     */
    fun getImagesDir(): File
    
    /**
     * 清理未使用的圖片
     * @param usedImageNames 正在使用的圖片文件名列表
     * @return 清理的文件數量
     */
    fun cleanupUnusedImages(usedImageNames: Set<String>): Int
}
```

**使用示例**：
```kotlin
val imageManager = ImageStorageManager(context)

// 保存圖片
val fileName = imageManager.saveImage(bitmap)

// 載入圖片
val loadedBitmap = imageManager.loadImage(fileName)

// 壓縮圖片
val compressedBitmap = imageManager.compressImage(bitmap, 800, 600)
```

### **BatchImportManager**

**用途**：處理 CSV 和 JSON 格式的批量匯入功能

**位置**：`utils/BatchImportManager.kt`

**主要方法**：

```kotlin
class BatchImportManager(private val context: Context) {
    
    /**
     * 匯入數據
     * @param uri 文件 URI
     * @param fileName 文件名
     * @return 匯入結果
     */
    suspend fun importData(uri: Uri, fileName: String): ImportResult
    
    /**
     * 從 ZIP 文件匯入
     * @param zipUri ZIP 文件 URI
     * @return 匯入結果
     */
    suspend fun importFromZip(zipUri: Uri): ImportResult
    
    /**
     * 驗證 CSV 格式
     * @param inputStream CSV 文件輸入流
     * @return 驗證結果
     */
    suspend fun validateCsvFormat(inputStream: InputStream): ValidationResult
    
    /**
     * 驗證 JSON 格式
     * @param inputStream JSON 文件輸入流
     * @return 驗證結果
     */
    suspend fun validateJsonFormat(inputStream: InputStream): ValidationResult
    
    /**
     * 獲取支援的文件格式
     * @return 支援的文件擴展名列表
     */
    fun getSupportedFormats(): List<String>
}

sealed class ImportResult {
    data class Success(
        val deckId: String,
        val deckName: String,
        val totalCount: Int,
        val successCount: Int,
        val errorCount: Int,
        val errors: List<String> = emptyList()
    ) : ImportResult()
    
    data class Error(val message: String) : ImportResult()
}

data class ValidationResult(
    val isValid: Boolean,
    val errors: List<String> = emptyList(),
    val warnings: List<String> = emptyList()
)
```

**CSV 格式要求**：
```csv
題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
解方程式 x + 5 = 12,,x = 7,,一元一次方程式,簡單,基本移項法則
```

**JSON 格式要求**：
```json
{
  "deckName": "數學題庫",
  "questions": [
    {
      "questionText": "解方程式 x + 5 = 12",
      "answer": "x = 7",
      "tags": ["一元一次方程式"],
      "difficulty": "EASY"
    }
  ]
}
```

### **OpenCVHelper**

**用途**：OpenCV 相關的圖像處理功能

**位置**：`utils/OpenCVHelper.kt`

**主要方法**：

```kotlin
object OpenCVHelper {
    
    /**
     * 初始化 OpenCV
     * @param context 應用程式上下文
     * @param callback 初始化完成回調
     */
    fun initializeOpenCV(context: Context, callback: (Boolean) -> Unit)
    
    /**
     * 檢測文檔邊界
     * @param bitmap 輸入圖片
     * @return 檢測到的四個角點，失敗返回 null
     */
    fun detectDocumentEdges(bitmap: Bitmap): List<Point>?
    
    /**
     * 透視變換校正
     * @param bitmap 輸入圖片
     * @param corners 四個角點
     * @return 校正後的圖片，失敗返回 null
     */
    fun perspectiveTransform(bitmap: Bitmap, corners: List<Point>): Bitmap?
    
    /**
     * 圖像增強
     * @param bitmap 輸入圖片
     * @param brightness 亮度調整 (-100 到 100)
     * @param contrast 對比度調整 (0.5 到 3.0)
     * @return 增強後的圖片
     */
    fun enhanceImage(bitmap: Bitmap, brightness: Int = 0, contrast: Float = 1.0f): Bitmap
    
    /**
     * 去除陰影
     * @param bitmap 輸入圖片
     * @return 去除陰影後的圖片
     */
    fun removeShadow(bitmap: Bitmap): Bitmap
    
    /**
     * 邊緣檢測
     * @param bitmap 輸入圖片
     * @param threshold1 第一個閾值
     * @param threshold2 第二個閾值
     * @return 邊緣檢測結果
     */
    fun detectEdges(bitmap: Bitmap, threshold1: Double = 50.0, threshold2: Double = 150.0): Bitmap
}
```

**使用示例**：
```kotlin
// 初始化 OpenCV
OpenCVHelper.initializeOpenCV(context) { success ->
    if (success) {
        // OpenCV 初始化成功
        val edges = OpenCVHelper.detectDocumentEdges(bitmap)
        edges?.let { corners ->
            val corrected = OpenCVHelper.perspectiveTransform(bitmap, corners)
        }
    }
}
```

### **CameraHelper**

**用途**：相機相關的工具函數

**位置**：`utils/CameraHelper.kt`

**主要方法**：

```kotlin
object CameraHelper {
    
    /**
     * 檢查相機權限
     * @param context 上下文
     * @return 是否有相機權限
     */
    fun hasCameraPermission(context: Context): Boolean
    
    /**
     * 請求相機權限
     * @param activity 活動
     * @param requestCode 請求碼
     */
    fun requestCameraPermission(activity: Activity, requestCode: Int)
    
    /**
     * 獲取最佳預覽尺寸
     * @param supportedSizes 支援的尺寸列表
     * @param targetWidth 目標寬度
     * @param targetHeight 目標高度
     * @return 最佳尺寸
     */
    fun getOptimalPreviewSize(
        supportedSizes: List<Size>,
        targetWidth: Int,
        targetHeight: Int
    ): Size
    
    /**
     * 計算圖片旋轉角度
     * @param context 上下文
     * @param cameraId 相機ID
     * @return 旋轉角度
     */
    fun getImageRotation(context: Context, cameraId: String): Int
    
    /**
     * 創建圖片輸出選項
     * @param outputFile 輸出文件
     * @return ImageCapture.OutputFileOptions
     */
    fun createOutputFileOptions(outputFile: File): ImageCapture.OutputFileOptions
}
```

## 🗄️ **數據管理 API**

### **DeckDataManager**

**用途**：卡組和卡片的數據管理

**位置**：`data/manager/DeckDataManager.kt`

**主要方法**：

```kotlin
class DeckDataManager private constructor(private val context: Context) {
    
    companion object {
        fun getInstance(context: Context): DeckDataManager
    }
    
    // 卡組操作
    fun getAllDecks(): List<StudyDeck>
    fun getDeck(id: String): StudyDeck?
    fun saveDeck(deck: StudyDeck): Boolean
    fun deleteDeck(id: String): Boolean
    fun generateNewDeckId(): String
    
    // 卡片操作
    fun getCardsInDeck(deckId: String): List<StudyCard>
    fun getCard(cardId: String, deckId: String): StudyCard?
    fun saveCard(card: StudyCard): Boolean
    fun deleteCard(cardId: String, deckId: String): Boolean
    fun generateNewCardId(): String
    
    // 統計和查詢
    fun getDeckStatistics(deckId: String): DeckStatistics?
    fun searchCards(query: String, deckId: String? = null): List<StudyCard>
    fun getCardsByTag(tag: String, deckId: String? = null): List<StudyCard>
    fun getCardsByDifficulty(difficulty: CardDifficulty, deckId: String? = null): List<StudyCard>
    
    // 學習相關
    fun updateStudyProgress(cardId: String, deckId: String, correct: Boolean)
    fun getCardsForReview(deckId: String): List<StudyCard>
    fun getNextReviewDate(card: StudyCard): Long
}
```

### **PreferencesManager**

**用途**：應用程式偏好設置管理

**位置**：`data/manager/PreferencesManager.kt`

**主要方法**：

```kotlin
class PreferencesManager(private val context: Context) {
    
    // 應用設置
    fun setTheme(theme: AppTheme)
    fun getTheme(): AppTheme
    fun setLanguage(language: String)
    fun getLanguage(): String
    
    // 學習設置
    fun setStudyMode(mode: StudyMode)
    fun getStudyMode(): StudyMode
    fun setAutoAdvance(enabled: Boolean)
    fun isAutoAdvanceEnabled(): Boolean
    fun setShuffleCards(enabled: Boolean)
    fun isShuffleCardsEnabled(): Boolean
    
    // 相機設置
    fun setCameraQuality(quality: CameraQuality)
    fun getCameraQuality(): CameraQuality
    fun setAutoCorrection(enabled: Boolean)
    fun isAutoCorrectionEnabled(): Boolean
    
    // 匯入設置
    fun setDefaultDifficulty(difficulty: CardDifficulty)
    fun getDefaultDifficulty(): CardDifficulty
    fun setAutoGenerateId(enabled: Boolean)
    fun isAutoGenerateIdEnabled(): Boolean
}

enum class AppTheme { LIGHT, DARK, SYSTEM }
enum class CameraQuality { LOW, MEDIUM, HIGH }
```

## 🎨 **UI 組件 API**

### **CommonButton**

**用途**：統一的按鈕組件

**位置**：`ui/components/CommonButton.kt`

```kotlin
@Composable
fun CommonButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    style: ButtonStyle = ButtonStyle.PRIMARY,
    icon: ImageVector? = null,
    loading: Boolean = false
)

enum class ButtonStyle {
    PRIMARY,    // 主要按鈕
    SECONDARY,  // 次要按鈕
    OUTLINE,    // 邊框按鈕
    TEXT        // 文字按鈕
}
```

### **CommonDialog**

**用途**：統一的對話框組件

**位置**：`ui/components/CommonDialog.kt`

```kotlin
@Composable
fun CommonDialog(
    title: String,
    message: String,
    onDismiss: () -> Unit,
    confirmButton: @Composable () -> Unit,
    dismissButton: @Composable (() -> Unit)? = null,
    icon: ImageVector? = null
)

@Composable
fun ConfirmDialog(
    title: String,
    message: String,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit,
    confirmText: String = "確認",
    dismissText: String = "取消"
)
```

### **LoadingIndicator**

**用途**：載入指示器組件

**位置**：`ui/components/LoadingIndicator.kt`

```kotlin
@Composable
fun LoadingIndicator(
    modifier: Modifier = Modifier,
    size: Dp = 48.dp,
    color: Color = MaterialTheme.colorScheme.primary
)

@Composable
fun LoadingOverlay(
    isLoading: Boolean,
    message: String = "載入中...",
    content: @Composable () -> Unit
)
```

## 🔍 **擴展函數 API**

### **Kotlin 擴展函數**

**位置**：`utils/Extensions.kt`

```kotlin
// String 擴展
fun String.isValidEmail(): Boolean
fun String.toCardDifficulty(): CardDifficulty
fun String.formatAsDate(): String

// Bitmap 擴展
fun Bitmap.resize(maxWidth: Int, maxHeight: Int): Bitmap
fun Bitmap.rotate(degrees: Float): Bitmap
fun Bitmap.toByteArray(format: Bitmap.CompressFormat = Bitmap.CompressFormat.JPEG): ByteArray

// Context 擴展
fun Context.showToast(message: String, duration: Int = Toast.LENGTH_SHORT)
fun Context.getColorCompat(@ColorRes colorRes: Int): Int
fun Context.dpToPx(dp: Float): Float

// Compose 擴展
fun Modifier.conditional(condition: Boolean, modifier: Modifier.() -> Modifier): Modifier
```

## 📱 **平台特定 API**

### **權限管理**

```kotlin
object PermissionHelper {
    
    fun checkPermission(context: Context, permission: String): Boolean
    fun requestPermissions(activity: Activity, permissions: Array<String>, requestCode: Int)
    fun shouldShowRequestPermissionRationale(activity: Activity, permission: String): Boolean
    
    // 常用權限
    const val CAMERA = Manifest.permission.CAMERA
    const val READ_EXTERNAL_STORAGE = Manifest.permission.READ_EXTERNAL_STORAGE
    const val WRITE_EXTERNAL_STORAGE = Manifest.permission.WRITE_EXTERNAL_STORAGE
}
```

### **文件操作**

```kotlin
object FileUtils {
    
    fun getFileExtension(fileName: String): String
    fun getMimeType(fileName: String): String
    fun isImageFile(fileName: String): Boolean
    fun createTempFile(context: Context, prefix: String, suffix: String): File
    fun copyFile(source: File, destination: File): Boolean
    fun deleteRecursively(file: File): Boolean
}
```

這份 API 參考文檔提供了所有核心工具類和組件的詳細使用說明，為開發者提供了完整的 API 指南。
