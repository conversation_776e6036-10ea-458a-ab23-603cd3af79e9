<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.erroranalysis.app"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="34" />

    <!-- 相機權限 -->
    <uses-permission android:name="android.permission.CAMERA" />

    <uses-feature
        android:name="android.hardware.camera"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />

    <!-- 存儲權限 -->
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <!-- 網絡權限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <permission
        android:name="com.erroranalysis.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.erroranalysis.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.erroranalysis.app.ErrorAnalysisApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:icon="@drawable/ic_app_icon"
        android:label="@string/app_name"
        android:roundIcon="@drawable/ic_app_icon"
        android:supportsRtl="true"
        android:theme="@style/Theme.ErrorAnalysisApp" >

        <!-- 主界面 -->
        <activity
            android:name="com.erroranalysis.app.ui.main.SimpleMainActivity"
            android:exported="true"
            android:theme="@style/Theme.ErrorAnalysisApp" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 相機界面 -->
        <activity
            android:name="com.erroranalysis.app.ui.camera.CameraActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!-- 簡化相機測試界面 -->
        <activity
            android:name="com.erroranalysis.app.ui.camera.SimpleCameraActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!-- 照片編輯界面 -->
        <activity
            android:name="com.erroranalysis.app.ui.camera.PhotoEditActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!-- 裁剪選取框測試界面 -->
        <activity
            android:name="com.erroranalysis.app.ui.camera.CropOverlayTestActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!-- 題目選擇界面 -->
        <activity
            android:name="com.erroranalysis.app.ui.selection.QuestionSelectionActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <!-- 分析結果界面 -->
        <activity
            android:name="com.erroranalysis.app.ui.analysis.AnalysisActivity"
            android:exported="false" />

        <!-- 錯題庫主界面（簡化版） -->
        <activity
            android:name="com.erroranalysis.app.ui.study.SimpleStudyActivity"
            android:configChanges="orientation|screenSize|screenLayout"
            android:exported="false"
            android:theme="@style/Theme.ErrorAnalysisApp" />

        <!-- 卡組詳細界面 -->
        <activity
            android:name="com.erroranalysis.app.ui.study.DeckDetailActivity"
            android:exported="false"
            android:parentActivityName="com.erroranalysis.app.ui.study.SimpleStudyActivity"
            android:theme="@style/Theme.ErrorAnalysisApp" />

        <!-- 卡片編輯界面 -->
        <activity
            android:name="com.erroranalysis.app.ui.study.CardEditActivity"
            android:exported="false"
            android:parentActivityName="com.erroranalysis.app.ui.study.DeckDetailActivity"
            android:theme="@style/Theme.ErrorAnalysisApp"
            android:windowSoftInputMode="adjustResize" />

        <!-- 卡片檢視界面 -->
        <activity
            android:name="com.erroranalysis.app.ui.study.CardViewerActivity"
            android:exported="false"
            android:parentActivityName="com.erroranalysis.app.ui.study.DeckDetailActivity"
            android:theme="@style/Theme.ErrorAnalysisApp" />

        <!-- 批次匯入界面 -->
        <activity
            android:name="com.erroranalysis.app.ui.study.BatchImportActivity"
            android:exported="false"
            android:parentActivityName="com.erroranalysis.app.ui.study.SimpleStudyActivity"
            android:theme="@style/Theme.ErrorAnalysisApp" />

        <!-- 設置頁面 -->
        <activity
            android:name="com.erroranalysis.app.ui.settings.SettingsActivity"
            android:exported="false"
            android:parentActivityName="com.erroranalysis.app.ui.study.SimpleStudyActivity"
            android:theme="@style/Theme.ErrorAnalysisApp" />

        <!--
        Service for holding metadata. Cannot be instantiated.
        Metadata will be merged from other manifests.
        -->
        <service
            android:name="androidx.camera.core.impl.MetadataHolderService"
            android:enabled="false"
            android:exported="false" >
            <meta-data
                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
        </service>
        <service
            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
            android:directBootAware="true"
            android:exported="false" >
            <meta-data
                android:name="com.google.firebase.components:com.google.mlkit.vision.text.internal.TextRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>

        <provider
            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
            android:authorities="com.erroranalysis.app.mlkitinitprovider"
            android:exported="false"
            android:initOrder="99" />

        <activity
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.erroranalysis.app.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
            android:exported="false" >
            <meta-data
                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
                android:value="cct" />
        </service>
        <service
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" >
        </service>

        <receiver
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
            android:exported="false" />
    </application>

</manifest>