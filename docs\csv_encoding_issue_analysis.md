# CSV編碼問題分析和解決方案

## 問題描述

用戶反映兩個問題：
1. `docs/數學-基礎代數.csv` 出現"第1行缺乏必要欄位"錯誤
2. 一直跳出"檔案 國小數學.csv: 沒有有效的題目資料"

## 問題根源分析

### 1. 編碼問題

**發現的問題**：
`docs/數學-基礎代數.csv` 檔案內容顯示為亂碼：
```
�D��ID,�D�ؤ�r,�D�عϤ�,����,���׹Ϥ�,����,����,����
Q001,,q001.jpg,5,,��¦�ƾ�,²��,�򥻥[�k�B��
```

**原因分析**：
- 檔案保存時使用了錯誤的編碼格式
- 可能是ANSI、GBK或其他非UTF-8編碼
- 導致中文字符變成亂碼

**解決方案**：
- 重新創建檔案，確保使用UTF-8編碼
- 添加編碼檢測功能，提前發現問題

### 2. 檔案名稱問題

**問題現象**：
系統顯示"國小數學.csv"而不是用戶期望的檔案名稱

**可能原因**：
1. **用戶實際選擇了該檔案**：檔案選擇器中確實有"國小數學.csv"
2. **檔案快取問題**：應用程式快取了之前的檔案
3. **檔案路徑解析問題**：URI解析錯誤
4. **檔案來源問題**：從其他應用分享或下載的檔案

## 修復措施

### 1. 修復範例檔案

**已修復的檔案**：`docs/數學-基礎代數.csv`
```csv
題目ID,題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
A001,解方程式 x + 5 = 12,,x = 7,,一元一次方程式,簡單,基本移項法則
A002,解方程式 2x - 3 = 7,,x = 5,,一元一次方程式,簡單,係數處理
A003,解方程式 x² - 4 = 0,,"x = 2 或 x = -2",,二次方程式,普通,平方差公式
...
```

### 2. 添加編碼檢測

**新增功能**：
```kotlin
// 檢查編碼問題
val hasEncodingIssues = csvContent.contains("�") || 
                       csvContent.contains("??") ||
                       csvContent.take(100).count { it.code > 127 } > csvContent.take(100).length * 0.5

if (hasEncodingIssues) {
    return ImportResult.Error(
        "CSV檔案「$fileName」可能有編碼問題\n\n" +
        "❌ 檔案內容包含亂碼字符\n\n" +
        "💡 請確認：\n" +
        "   • 檔案是否使用UTF-8編碼保存\n" +
        "   • 檔案是否在傳輸過程中損壞\n" +
        "   • 嘗試重新創建CSV檔案"
    )
}
```

### 3. 改進檔案名稱追蹤

**詳細日誌**：
```kotlin
Log.d(TAG, "獲取檔案名稱，URI: $uri")
Log.d(TAG, "從ContentResolver獲取檔案名稱: $name")
Log.d(TAG, "嘗試從URI路徑獲取檔案名稱: $lastSegment")
```

**多重獲取策略**：
1. 優先從ContentResolver獲取DISPLAY_NAME
2. 如果失敗，從URI路徑獲取
3. 記錄詳細的獲取過程

### 4. 改進錯誤信息

**"沒有有效的題目資料"錯誤**：
```
CSV檔案「國小數學.csv」沒有有效的題目資料

📊 處理統計：
   • 總行數：5
   • 標題行：1行
   • 資料行：4行
   • 成功解析：0行
   • 解析錯誤：4行

❌ 錯誤詳情：
   • 第2行錯誤：答案欄位（第4列）不能為空
   • 第3行欄位數量不匹配：期望8個欄位，實際6個欄位
   • 第4行錯誤：題目ID欄位（第1列）格式錯誤
   • 第5行解析失敗：Invalid character

💡 請檢查：
   • 檔案編碼是否為UTF-8
   • 必要欄位是否填寫完整
   • CSV格式是否正確
```

## 測試和驗證

### 1. 編碼測試

**測試檔案**：
- ✅ `docs/數學-基礎代數.csv` - 已修復，UTF-8編碼
- ✅ `docs/物理-力學基礎.csv` - 正常，UTF-8編碼
- ✅ `docs/English-Basic_Grammar.csv` - 正常，UTF-8編碼

**測試步驟**：
1. 使用修復後的CSV檔案測試匯入
2. 故意創建編碼錯誤的檔案測試檢測功能
3. 確認錯誤信息清晰準確

### 2. 檔案名稱測試

**測試場景**：
1. 直接選擇本地CSV檔案
2. 從ZIP檔案中匯入CSV
3. 從其他應用分享的檔案
4. 從雲端下載的檔案

**檢查項目**：
- logcat中的檔案名稱日誌
- 錯誤信息中顯示的檔案名稱
- 實際選擇的檔案與顯示名稱是否一致

## 預防措施

### 1. 檔案創建指南

**推薦工具**：
- 使用支援UTF-8的文字編輯器（如VS Code、Notepad++）
- 避免使用記事本（可能保存為ANSI編碼）
- 使用Excel時選擇"UTF-8 CSV"格式

**檢查方法**：
- 用文字編輯器打開CSV檔案檢查中文顯示
- 確認檔案大小合理（亂碼通常會導致異常大小）
- 使用`file`命令檢查檔案編碼（Linux/Mac）

### 2. 匯入前檢查

**用戶操作建議**：
1. 匯入前用文字編輯器預覽CSV檔案
2. 確認中文字符顯示正常
3. 檢查必要欄位是否存在
4. 確認檔案名稱正確

### 3. 錯誤處理改進

**應用程式改進**：
- 提供更詳細的錯誤診斷
- 支援多種編碼格式的自動檢測
- 提供檔案修復建議
- 記錄完整的匯入過程日誌

## 常見問題解答

### Q1: 為什麼會出現"國小數學.csv"？
**A**: 可能的原因：
1. 用戶實際選擇了該檔案
2. 檔案選擇器快取問題
3. 從其他來源獲得的檔案
4. 檔案路徑解析錯誤

**解決方法**：
- 檢查logcat日誌確認實際檔案名稱
- 清除應用程式快取
- 重新選擇正確的檔案

### Q2: 如何確保CSV檔案編碼正確？
**A**: 
1. 使用VS Code、Notepad++等編輯器
2. 保存時選擇UTF-8編碼
3. 避免使用Windows記事本
4. 匯入前預覽檔案內容

### Q3: 如何解決"沒有有效的題目資料"？
**A**: 
1. 檢查錯誤詳情中的具體問題
2. 確認必要欄位（題目ID、答案）存在且不為空
3. 檢查CSV格式是否正確
4. 確認檔案編碼為UTF-8

### Q4: 如何創建正確的CSV檔案？
**A**: 
1. 使用提供的範例檔案作為模板
2. 確保第一行包含完整的欄位標題
3. 必要欄位：題目ID、答案
4. 可選欄位：題目文字、圖片、標籤、難度、說明
5. 使用UTF-8編碼保存

這個分析和解決方案應該能幫助用戶理解和解決CSV匯入的編碼和檔案名稱問題！
