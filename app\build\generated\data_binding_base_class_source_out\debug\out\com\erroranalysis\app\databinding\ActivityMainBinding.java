// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final BottomNavigationView bottomNavigation;

  @NonNull
  public final MaterialCardView btnCamera;

  @NonNull
  public final MaterialCardView btnErrorBank;

  @NonNull
  public final MaterialCardView btnPractice;

  @NonNull
  public final MaterialCardView btnReport;

  @NonNull
  public final LinearLayout layoutHeader;

  @NonNull
  public final TextView tvAnalyzedCount;

  @NonNull
  public final TextView tvErrorCount;

  private ActivityMainBinding(@NonNull ConstraintLayout rootView,
      @NonNull BottomNavigationView bottomNavigation, @NonNull MaterialCardView btnCamera,
      @NonNull MaterialCardView btnErrorBank, @NonNull MaterialCardView btnPractice,
      @NonNull MaterialCardView btnReport, @NonNull LinearLayout layoutHeader,
      @NonNull TextView tvAnalyzedCount, @NonNull TextView tvErrorCount) {
    this.rootView = rootView;
    this.bottomNavigation = bottomNavigation;
    this.btnCamera = btnCamera;
    this.btnErrorBank = btnErrorBank;
    this.btnPractice = btnPractice;
    this.btnReport = btnReport;
    this.layoutHeader = layoutHeader;
    this.tvAnalyzedCount = tvAnalyzedCount;
    this.tvErrorCount = tvErrorCount;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bottom_navigation;
      BottomNavigationView bottomNavigation = ViewBindings.findChildViewById(rootView, id);
      if (bottomNavigation == null) {
        break missingId;
      }

      id = R.id.btn_camera;
      MaterialCardView btnCamera = ViewBindings.findChildViewById(rootView, id);
      if (btnCamera == null) {
        break missingId;
      }

      id = R.id.btn_error_bank;
      MaterialCardView btnErrorBank = ViewBindings.findChildViewById(rootView, id);
      if (btnErrorBank == null) {
        break missingId;
      }

      id = R.id.btn_practice;
      MaterialCardView btnPractice = ViewBindings.findChildViewById(rootView, id);
      if (btnPractice == null) {
        break missingId;
      }

      id = R.id.btn_report;
      MaterialCardView btnReport = ViewBindings.findChildViewById(rootView, id);
      if (btnReport == null) {
        break missingId;
      }

      id = R.id.layout_header;
      LinearLayout layoutHeader = ViewBindings.findChildViewById(rootView, id);
      if (layoutHeader == null) {
        break missingId;
      }

      id = R.id.tv_analyzed_count;
      TextView tvAnalyzedCount = ViewBindings.findChildViewById(rootView, id);
      if (tvAnalyzedCount == null) {
        break missingId;
      }

      id = R.id.tv_error_count;
      TextView tvErrorCount = ViewBindings.findChildViewById(rootView, id);
      if (tvErrorCount == null) {
        break missingId;
      }

      return new ActivityMainBinding((ConstraintLayout) rootView, bottomNavigation, btnCamera,
          btnErrorBank, btnPractice, btnReport, layoutHeader, tvAnalyzedCount, tvErrorCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
