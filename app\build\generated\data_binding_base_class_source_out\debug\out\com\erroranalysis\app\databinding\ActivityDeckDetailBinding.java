// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityDeckDetailBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final ImageButton btnFilter;

  @NonNull
  public final FloatingActionButton fabCreateCard;

  @NonNull
  public final LinearLayout layoutEmpty;

  @NonNull
  public final RecyclerView recyclerCards;

  @NonNull
  public final TextView textCardCount;

  @NonNull
  public final TextView textDeckTitle;

  @NonNull
  public final Toolbar toolbar;

  private ActivityDeckDetailBinding(@NonNull CoordinatorLayout rootView,
      @NonNull ImageButton btnFilter, @NonNull FloatingActionButton fabCreateCard,
      @NonNull LinearLayout layoutEmpty, @NonNull RecyclerView recyclerCards,
      @NonNull TextView textCardCount, @NonNull TextView textDeckTitle, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.btnFilter = btnFilter;
    this.fabCreateCard = fabCreateCard;
    this.layoutEmpty = layoutEmpty;
    this.recyclerCards = recyclerCards;
    this.textCardCount = textCardCount;
    this.textDeckTitle = textDeckTitle;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityDeckDetailBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityDeckDetailBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_deck_detail, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityDeckDetailBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_filter;
      ImageButton btnFilter = ViewBindings.findChildViewById(rootView, id);
      if (btnFilter == null) {
        break missingId;
      }

      id = R.id.fab_create_card;
      FloatingActionButton fabCreateCard = ViewBindings.findChildViewById(rootView, id);
      if (fabCreateCard == null) {
        break missingId;
      }

      id = R.id.layout_empty;
      LinearLayout layoutEmpty = ViewBindings.findChildViewById(rootView, id);
      if (layoutEmpty == null) {
        break missingId;
      }

      id = R.id.recycler_cards;
      RecyclerView recyclerCards = ViewBindings.findChildViewById(rootView, id);
      if (recyclerCards == null) {
        break missingId;
      }

      id = R.id.text_card_count;
      TextView textCardCount = ViewBindings.findChildViewById(rootView, id);
      if (textCardCount == null) {
        break missingId;
      }

      id = R.id.text_deck_title;
      TextView textDeckTitle = ViewBindings.findChildViewById(rootView, id);
      if (textDeckTitle == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityDeckDetailBinding((CoordinatorLayout) rootView, btnFilter, fabCreateCard,
          layoutEmpty, recyclerCards, textCardCount, textDeckTitle, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
