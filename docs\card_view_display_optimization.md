# 卡片檢視顯示優化

## 優化內容

本次優化包含三個主要改進：
1. 移除答案和AI解答之間的分隔線
2. 調整答案顯示區寬度到最大
3. 處理AI回答中的Markdown格式符號

## 1. 移除分隔線

### 改進前
```
📝 答案
用戶設定的答案內容

━━━━━━━━━━━━━━━━━━━━
🤖 AI解答
AI生成的詳細解答
```

### 改進後
```
📝 答案
用戶設定的答案內容

🤖 AI解答
AI生成的詳細解答
```

### 技術實現
```kotlin
// 移除分隔線，直接添加AI解答標題
if (card.aiAnswer.isNotEmpty()) {
    contentList.add(mapOf("type" to "text", "content" to "\n\n🤖 AI解答"))
    // ... AI解答內容
}
```

### 優勢
- **視覺簡潔**：減少視覺干擾，內容更清爽
- **空間利用**：節省顯示空間，更多內容可見
- **閱讀流暢**：答案和AI解答之間過渡更自然

## 2. 最大化顯示區寬度

### 改進前
- 內容區域有較大的padding（20dp）
- 滾動條佔用額外空間
- 內容顯示區域相對較窄

### 改進後
- 減少padding到16dp
- 移除滾動條佔用的空間
- 添加明確的margin設置確保最大寬度

### 技術實現
```xml
<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <RichTextEditText
        android:id="@+id/edit_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="0dp"
        android:layout_marginEnd="0dp"
        android:scrollbars="none" />
</LinearLayout>
```

### 優勢
- **更多內容**：每行可顯示更多文字
- **更好利用**：充分利用螢幕寬度
- **視覺平衡**：內容與邊框比例更協調

## 3. Markdown格式處理

### 問題分析
AI回答中常常包含Markdown格式符號：
- `**粗體文字**`
- `*斜體文字*`
- `# 標題`
- `` `代碼` ``
- `[連結](url)`
- `![圖片](url)`

這些符號在純文字顯示中會影響閱讀體驗。

### 處理策略
採用**移除符號**的策略，保留內容但移除格式標記：

```kotlin
private fun processMarkdownContent(content: String): String {
    var processedContent = content
    
    // 移除粗體標記 **text** 和 __text__
    processedContent = processedContent
        .replace(Regex("\\*\\*([^*]+)\\*\\*"), "$1")
        .replace(Regex("__([^_]+)__"), "$1")
        
    // 移除斜體標記 *text* 和 _text_
        .replace(Regex("(?<!\\*)\\*([^*]+)\\*(?!\\*)"), "$1")
        .replace(Regex("(?<!_)_([^_]+)_(?!_)"), "$1")
        
    // 移除其他Markdown符號...
    
    return processedContent
}
```

### 支援的Markdown格式

#### 文字格式
- **粗體**：`**text**` → `text`
- **斜體**：`*text*` → `text`
- **代碼**：`` `code` `` → `code`

#### 結構元素
- **標題**：`# Title` → `Title`
- **列表**：`- item` → `• item`
- **引用**：`> quote` → `quote`

#### 連結和圖片
- **連結**：`[text](url)` → `text`
- **圖片**：`![alt](url)` → `alt`

#### 代碼塊
- **代碼塊**：移除整個代碼塊
- **行內代碼**：保留代碼內容

### 處理範例

#### 範例1：基本格式
```
輸入：**步驟1：**分析題目
輸出：步驟1：分析題目
```

#### 範例2：混合格式
```
輸入：根據*牛頓第二定律*，我們知道 **F = ma**
輸出：根據牛頓第二定律，我們知道 F = ma
```

#### 範例3：列表格式
```
輸入：
- 第一步：分析
- 第二步：計算
- 第三步：驗證

輸出：
• 第一步：分析
• 第二步：計算  
• 第三步：驗證
```

#### 範例4：標題格式
```
輸入：
## 解題步驟
### 分析階段

輸出：
解題步驟
分析階段
```

## 技術實現細節

### 1. 正則表達式說明

#### 粗體處理
```kotlin
.replace(Regex("\\*\\*([^*]+)\\*\\*"), "$1")
```
- 匹配：`**文字**`
- 提取：括號內的文字
- 替換：只保留文字內容

#### 斜體處理（避免與粗體衝突）
```kotlin
.replace(Regex("(?<!\\*)\\*([^*]+)\\*(?!\\*)"), "$1")
```
- 負向後瞻：`(?<!\\*)`確保前面不是`*`
- 負向前瞻：`(?!\\*)`確保後面不是`*`
- 避免誤處理粗體標記

#### 標題處理
```kotlin
.replace(Regex("^#{1,6}\\s*"), "")
```
- 匹配行首的1-6個`#`符號
- 包含後面的空格
- 完全移除標題標記

### 2. 處理順序
1. **粗體** → **斜體** → **代碼** → **連結** → **列表** → **其他**
2. 先處理複雜格式，再處理簡單格式
3. 避免處理順序導致的格式衝突

### 3. 錯誤處理
```kotlin
try {
    val processedContent = processMarkdownContent(content)
    contentList.add(mapOf("type" to "text", "content" to processedContent))
} catch (e: Exception) {
    // 如果處理失敗，使用原始內容
    contentList.add(mapOf("type" to "text", "content" to content))
}
```

## 整體效果

### 視覺改進
- **更簡潔**：移除不必要的分隔線和格式符號
- **更寬敞**：最大化利用顯示空間
- **更清晰**：內容層次分明，易於閱讀

### 閱讀體驗
- **流暢性**：答案和AI解答之間過渡自然
- **可讀性**：移除干擾性的Markdown符號
- **完整性**：保留所有重要內容信息

### 技術優勢
- **穩定性**：多層錯誤處理確保穩定運行
- **兼容性**：支援各種Markdown格式變體
- **可維護性**：清晰的處理邏輯便於維護

## 測試建議

### 1. 基本顯示測試
- 確認分隔線已移除
- 確認內容區域寬度最大化
- 確認Markdown符號已清理

### 2. Markdown處理測試
- 測試包含`**粗體**`的AI回答
- 測試包含`*斜體*`的AI回答
- 測試包含`# 標題`的AI回答
- 測試包含列表的AI回答

### 3. 邊界情況測試
- 測試只有答案沒有AI解答的卡片
- 測試只有AI解答沒有答案的卡片
- 測試包含複雜Markdown格式的內容
- 測試Markdown處理失敗的降級情況

### 4. 視覺效果測試
- 確認內容顯示寬度充分利用螢幕
- 確認答案和AI解答之間間距適當
- 確認長內容的滾動體驗流暢

這些優化讓卡片檢視功能更加簡潔、實用，提供了更好的閱讀體驗！
