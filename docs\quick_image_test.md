# 圖片匯入問題快速診斷

## 🎯 目標

快速診斷為什麼只有圖片的題目沒有被匯入，但系統顯示匯入成功。

## 📱 測試步驟

### 步驟1：準備最簡單的測試檔案

創建一個只有2行的CSV檔案：

**test_simple.csv**：
```csv
題目ID,題目文字,題目圖片,答案
Q001,有文字的題目,test1.jpg,答案1
Q002,,test2.jpg,答案2
```

### 步驟2：創建ZIP檔案

```
test_simple.zip
├── test_simple.csv
└── images/
    ├── test1.jpg  (任何JPG圖片)
    └── test2.jpg  (任何JPG圖片)
```

### 步驟3：執行匯入並收集關鍵日誌

```bash
# 清除舊日誌
adb logcat -c

# 開始收集日誌
adb logcat | grep "BatchImportManager" > debug.log &

# 執行匯入操作
# (在手機上選擇test_simple.zip並匯入)

# 停止日誌收集 (Ctrl+C)
```

### 步驟4：檢查關鍵日誌

#### 4.1 檢查欄位映射
查找：
```
=== 建立欄位映射 ===
原始標題: [題目ID, 題目文字, 題目圖片, 答案]
處理標題[0]: '題目ID' -> '題目id'
  ✅ 映射到 id
處理標題[1]: '題目文字' -> '題目文字'
  ✅ 映射到 questionText
處理標題[2]: '題目圖片' -> '題目圖片'
  ✅ 映射到 questionImage
處理標題[3]: '答案' -> '答案'
  ✅ 映射到 answer
最終欄位映射: {id=0, questionText=1, questionImage=2, answer=3}
```

**如果看到**：
- `❌ 未映射` 在題目圖片欄位 → 欄位名稱問題
- `questionImage` 沒有出現在最終映射中 → 欄位識別失敗

#### 4.2 檢查Q002的解析過程
查找：
```
=== 題目解析詳情 ===
CSV行值: [Q002, , test2.jpg, 答案2]
questionText索引: 1
questionImage索引: 2
題目文字原始值: ''
題目圖片原始值: 'test2.jpg'
解析結果 - 文字: '', 圖片: 'test2.jpg'

getValue: field='questionText', index=1, rawValue='', trimmedValue=''
getValue: field='questionImage', index=2, rawValue='test2.jpg', trimmedValue='test2.jpg'

驗證題目內容：
  questionText.isEmpty() = true
  questionImage.isEmpty() = false
  questionText = ''
  questionImage = 'test2.jpg'
✅ 檢測到只有圖片的題目：test2.jpg
   這個題目應該會被正常匯入
```

**如果看到**：
- `questionImage.isEmpty() = true` → 圖片欄位值獲取失敗
- `❌ 題目文字和圖片都為空，跳過此題目` → 題目被跳過

#### 4.3 檢查匯入統計
查找：
```
CSV解析完成，共2個題目，0個錯誤
```

**如果看到**：
- `共1個題目，1個錯誤` → Q002被跳過並計入錯誤
- `共2個題目，0個錯誤` → 兩個題目都應該被匯入

#### 4.4 檢查最終結果
查找：
```
✅ 匯入成功：測試
   成功：2/2
```

**如果看到**：
- `成功：1/2` → 只有Q001被匯入
- `成功：2/2` → 兩個題目都被匯入

### 步驟5：檢查應用程式中的結果

1. 進入新創建的卡組
2. 檢查卡片數量：
   - **應該有2張卡片**
   - 如果只有1張，說明Q002沒有被匯入

## 🔍 可能的問題

### 問題1：欄位映射失敗
**症狀**：`questionImage` 沒有出現在最終映射中
**原因**：CSV標題行的"題目圖片"欄位名稱不匹配
**解決**：確認CSV第一行確實是 `題目ID,題目文字,題目圖片,答案`

### 問題2：圖片欄位值為空
**症狀**：`questionImage.isEmpty() = true`
**原因**：CSV解析問題或欄位值確實為空
**解決**：檢查CSV檔案中Q002行的第3個欄位是否確實是 `test2.jpg`

### 問題3：題目被跳過
**症狀**：`❌ 題目文字和圖片都為空，跳過此題目`
**原因**：驗證邏輯認為兩個欄位都為空
**解決**：檢查前面的日誌，確認圖片欄位值獲取過程

### 問題4：統計不一致
**症狀**：日誌顯示2個題目，但卡組只有1張卡片
**原因**：題目在後續處理中被丟棄
**解決**：檢查圖片處理和卡片創建過程

## 📋 快速檢查清單

### CSV檔案檢查
- [ ] 第一行確實是：`題目ID,題目文字,題目圖片,答案`
- [ ] 第三行確實是：`Q002,,test2.jpg,答案2`
- [ ] 檔案編碼為UTF-8
- [ ] 沒有多餘的空格或特殊字符

### ZIP檔案檢查
- [ ] 包含`images/`目錄
- [ ] `test2.jpg`存在於`images/`目錄中
- [ ] 圖片檔案沒有損壞

### 日誌檢查
- [ ] 欄位映射包含`questionImage=2`
- [ ] Q002的圖片欄位值為`test2.jpg`
- [ ] 驗證通過，顯示"檢測到只有圖片的題目"
- [ ] 統計顯示2個題目成功匯入

### 結果檢查
- [ ] 卡組包含2張卡片
- [ ] Q002卡片存在且包含圖片

## 🎯 預期結果

如果一切正常，您應該看到：

1. **日誌中**：
   - 欄位映射正確
   - Q002被識別為只有圖片的題目
   - 統計顯示2/2成功

2. **應用程式中**：
   - 卡組包含2張卡片
   - 第2張卡片只有圖片，沒有文字

如果結果不符合預期，請將完整的`debug.log`檔案提供給我分析。

## 📞 下一步

1. **立即測試**：使用這個簡化的測試檔案
2. **收集日誌**：按照步驟3收集完整日誌
3. **分析結果**：按照步驟4檢查關鍵日誌點
4. **回報問題**：如果問題仍存在，提供日誌檔案

這個簡化的測試應該能夠快速定位問題的根源。
