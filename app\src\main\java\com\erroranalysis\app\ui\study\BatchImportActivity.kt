package com.erroranalysis.app.ui.study

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.erroranalysis.app.databinding.ActivityBatchImportBinding
import com.erroranalysis.app.utils.BatchImportManager
import com.erroranalysis.app.utils.ImportResult
import kotlinx.coroutines.launch

/**
 * 批次匯入Activity
 */
class BatchImportActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityBatchImportBinding
    private lateinit var importManager: BatchImportManager
    
    // 檔案選擇器
    private val filePickerLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let { importQuestionBank(it) }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityBatchImportBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // 初始化
        importManager = BatchImportManager(this)
        
        // 設置工具列
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "批次匯入題庫"
        
        setupUI()
    }
    
    private fun setupUI() {
        // 選擇檔案按鈕
        binding.buttonSelectFile.setOnClickListener {
            selectFile()
        }
        
        // 匯入說明
        binding.textInstructions.text = """
            📋 支援的檔案格式：
            • CSV檔案（推薦）- 直接匯入，檔名作為卡組名稱
            • ZIP壓縮包（包含多個CSV檔案和images資料夾）
            • JSON檔案（純文字題目）

            📁 檔案結構範例：

            單一CSV檔案：
            數學-代數基礎.csv

            ZIP壓縮包：
            題庫包.zip
            ├── 數學-代數基礎.csv
            ├── 物理-力學基礎.csv
            ├── English-Basic_Grammar.csv
            └── images/
                ├── diagram1.jpg
                └── formula2.png

            🖼️ 圖片支援：
            • 題目圖片、答案圖片欄位可為空
            • 圖片檔案需放在images目錄下
            • 支援jpg、png等常見格式

            💡 推薦使用ZIP格式批次匯入多個科目
        """.trimIndent()
        
        // 範例按鈕
        binding.buttonShowExample.setOnClickListener {
            showExampleFormat()
        }
    }
    
    /**
     * 選擇檔案
     */
    private fun selectFile() {
        try {
            // 支援多種格式：CSV、JSON、ZIP
            filePickerLauncher.launch("*/*")
        } catch (e: Exception) {
            Toast.makeText(this, "無法開啟檔案選擇器：${e.message}", Toast.LENGTH_LONG).show()
        }
    }
    
    /**
     * 匯入題庫檔案
     */
    private fun importQuestionBank(uri: Uri) {
        android.util.Log.d("BatchImportActivity", "=== 開始匯入檔案 ===")
        android.util.Log.d("BatchImportActivity", "URI: $uri")

        // 顯示進度
        showProgress(true)
        binding.textStatus.text = "正在解析檔案..."
        
        lifecycleScope.launch {
            try {
                val result = importManager.importQuestionBank(uri)
                
                runOnUiThread {
                    showProgress(false)
                    handleImportResult(result)
                }
                
            } catch (e: Exception) {
                android.util.Log.e("BatchImport", "匯入過程發生異常", e)
                runOnUiThread {
                    showProgress(false)
                    val errorMessage = """
                        ❌ 匯入失敗：${e.message}

                        錯誤類型：${e.javaClass.simpleName}

                        請檢查：
                        1. 檔案格式是否正確（支援CSV、JSON、ZIP）
                        2. 檔案是否損壞
                        3. 檔案編碼是否為UTF-8
                        4. CSV檔案是否包含必要欄位（題目ID、答案）
                    """.trimIndent()

                    binding.textStatus.text = errorMessage
                    Toast.makeText(this@BatchImportActivity, "匯入失敗：${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        }
    }
    
    /**
     * 處理匯入結果
     */
    private fun handleImportResult(result: ImportResult) {
        when (result) {
            is ImportResult.Success -> {
                binding.textStatus.text = """
                    ✅ 題庫匯入完成！

                    📚 已建立卡組：${result.deckName}
                    🆔 卡組ID：${result.deckId}
                    📊 總題目數：${result.totalCount}
                    ✅ 成功匯入：${result.successCount} 張卡片
                    ❌ 失敗數量：${result.errorCount}

                    💡 您可以在主頁面看到新建立的卡組
                """.trimIndent()
                
                if (result.errors.isNotEmpty()) {
                    val errorText = "錯誤詳情：\n" + result.errors.joinToString("\n")
                    android.util.Log.d("BatchImport", "顯示錯誤詳情: ${result.errors.size} 個錯誤")
                    android.util.Log.d("BatchImport", "錯誤內容: $errorText")

                    binding.textErrors.text = errorText
                    binding.textErrors.visibility = android.view.View.VISIBLE

                    // 確保父容器也是可見的
                    binding.textErrors.parent?.let { parent ->
                        if (parent is android.view.View) {
                            parent.visibility = android.view.View.VISIBLE
                            android.util.Log.d("BatchImport", "設置父容器可見")
                        }
                    }
                } else {
                    android.util.Log.d("BatchImport", "沒有錯誤，隱藏錯誤詳情")
                    binding.textErrors.visibility = android.view.View.GONE
                }
                
                // 顯示完成按鈕
                binding.buttonComplete.visibility = android.view.View.VISIBLE
                binding.buttonComplete.text = "✅ 完成匯入"
                binding.buttonComplete.setOnClickListener {
                    setResult(RESULT_OK)
                    finish()
                }

                // 顯示查看卡組按鈕
                binding.buttonViewDeck.visibility = android.view.View.VISIBLE
                binding.buttonViewDeck.setOnClickListener {
                    // 跳轉到卡組詳細頁面
                    val intent = Intent(this@BatchImportActivity, DeckDetailActivity::class.java)
                    intent.putExtra("deck_id", result.deckId)
                    intent.putExtra("deck_name", result.deckName)
                    startActivity(intent)

                    setResult(RESULT_OK)
                    finish()
                }
                
                Toast.makeText(this, "題庫匯入成功！", Toast.LENGTH_LONG).show()
            }
            
            is ImportResult.Error -> {
                binding.textStatus.text = "❌ 匯入失敗"

                // 顯示詳細的錯誤信息
                android.util.Log.d("BatchImport", "設置錯誤信息: ${result.message}")
                binding.textErrors.text = result.message
                binding.textErrors.visibility = android.view.View.VISIBLE

                // 確保父容器也是可見的
                binding.textErrors.parent?.let { parent ->
                    if (parent is android.view.View) {
                        parent.visibility = android.view.View.VISIBLE
                        android.util.Log.d("BatchImport", "設置父容器可見")
                    }
                }

                android.util.Log.d("BatchImport", "錯誤詳情可見性: ${binding.textErrors.visibility}")

                Toast.makeText(this, "匯入失敗", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    /**
     * 顯示/隱藏進度
     */
    private fun showProgress(show: Boolean) {
        binding.progressBar.visibility = if (show) android.view.View.VISIBLE else android.view.View.GONE
        binding.buttonSelectFile.isEnabled = !show
    }
    
    /**
     * 顯示範例格式
     */
    private fun showExampleFormat() {
        val exampleCsv = """
            📋 CSV格式範例：

            題目ID,題目文字,題目圖片,答案,答案圖片,標籤,難度,說明
            A001,解方程式 x + 5 = 12,,x = 7,,一元一次方程式,簡單,基本移項法則
            P001,質量為2kg的物體受到10N的力，求加速度,force.jpg,5m/s²,calc.jpg,牛頓第二定律,簡單,F = ma
            E001,Choose: I ___ to school every day.,,go,,現在式,簡單,一般現在式的用法

            📁 ZIP檔案結構範例：
            題庫包.zip
            ├── 數學-代數基礎.csv
            ├── 物理-力學基礎.csv
            ├── English-Basic_Grammar.csv
            └── images/
                ├── force.jpg
                ├── calc.jpg
                └── diagram.png

            💡 提示：
            • 檔案名稱會成為卡組名稱
            • 必要欄位：題目ID、答案
            • 圖片欄位：題目圖片、答案圖片（可為空）
            • ZIP可包含多個CSV檔案，一次匯入多個科目
        """.trimIndent()

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("CSV格式範例")
            .setMessage(exampleCsv)
            .setPositiveButton("確定", null)
            .show()
    }
    
    override fun onSupportNavigateUp(): Boolean {
        finish()
        return true
    }
}
