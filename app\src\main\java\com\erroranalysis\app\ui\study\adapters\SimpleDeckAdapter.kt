package com.erroranalysis.app.ui.study.adapters

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.erroranalysis.app.databinding.ItemSimpleDeckBinding
import com.erroranalysis.app.ui.study.SimpleDeck

/**
 * 簡化版卡組適配器
 */
class SimpleDeckAdapter(
    private val onDeckClick: (SimpleDeck) -> Unit,
    private val onDeckLongClick: (SimpleDeck) -> Unit
) : ListAdapter<SimpleDeck, SimpleDeckAdapter.DeckViewHolder>(DeckDiffCallback()) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DeckViewHolder {
        val binding = ItemSimpleDeckBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return DeckViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: DeckViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    inner class DeckViewHolder(
        private val binding: ItemSimpleDeckBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(deck: SimpleDeck) {
            binding.textDeckName.text = deck.name
            binding.textCardCount.text = "${deck.cardCount} 張"
            binding.textCoverIcon.text = deck.icon
            
            // 設置封面背景顏色
            try {
                val color = Color.parseColor(deck.color)
                val darkerColor = darkenColor(color, 0.3f)
                val gradient = GradientDrawable(
                    GradientDrawable.Orientation.TOP_BOTTOM,
                    intArrayOf(color, darkerColor)
                )
                gradient.cornerRadius = 16f
                binding.cardCover.background = gradient
            } catch (e: Exception) {
                // 使用預設背景
            }
            
            // 設置點擊事件
            binding.root.setOnClickListener {
                onDeckClick(deck)
            }
            
            binding.root.setOnLongClickListener {
                onDeckLongClick(deck)
                true
            }
        }
        
        private fun darkenColor(color: Int, factor: Float): Int {
            val a = Color.alpha(color)
            val r = (Color.red(color) * (1 - factor)).toInt()
            val g = (Color.green(color) * (1 - factor)).toInt()
            val b = (Color.blue(color) * (1 - factor)).toInt()
            return Color.argb(a, r, g, b)
        }
    }
    
    private class DeckDiffCallback : DiffUtil.ItemCallback<SimpleDeck>() {
        override fun areItemsTheSame(oldItem: SimpleDeck, newItem: SimpleDeck): Boolean {
            return oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: SimpleDeck, newItem: SimpleDeck): Boolean {
            return oldItem == newItem
        }
    }
}
