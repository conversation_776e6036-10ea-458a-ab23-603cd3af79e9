android.view.View+androidx.camera.core.ImageAnalysis.Analyzer?com.erroranalysis.app.ui.theme.ThemeManager.ThemeChangeListener,com.erroranalysis.app.ui.base.ThemedActivityandroid.os.Parcelablekotlin.Enum1androidx.recyclerview.widget.RecyclerView.Adapter(androidx.recyclerview.widget.ListAdapter4androidx.recyclerview.widget.RecyclerView.ViewHolder2androidx.recyclerview.widget.DiffUtil.ItemCallback(androidx.appcompat.app.AppCompatActivity(com.erroranalysis.app.utils.ImportResult1com.erroranalysis.app.utils.DocumentProcessResult androidx.viewbinding.ViewBindingandroid.app.Applicationandroidx.lifecycle.ViewModel7com.google.android.material.textfield.TextInputEditTextandroid.text.style.ImageSpan8androidx.recyclerview.widget.RecyclerView.ItemDecoration                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        