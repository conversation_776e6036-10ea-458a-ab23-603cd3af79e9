<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_theme"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="6dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="16dp"
    app:cardElevation="2dp"
    app:strokeColor="@color/primary_blue"
    app:strokeWidth="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- 主題預覽 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginEnd="16dp">

            <View
                android:id="@+id/view_theme_preview"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/text_theme_emoji"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="💙"
                android:textSize="16sp" />

        </LinearLayout>

        <!-- 主題信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/text_theme_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="經典藍"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary" />

            <TextView
                android:id="@+id/text_theme_description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="沉穩專業的藍色主題"
                android:textSize="14sp"
                android:textColor="@color/text_secondary" />

        </LinearLayout>

        <!-- 選中指示器 -->
        <ImageView
            android:id="@+id/icon_selected"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_check_circle"
            android:visibility="gone"
            app:tint="@color/success_green" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
