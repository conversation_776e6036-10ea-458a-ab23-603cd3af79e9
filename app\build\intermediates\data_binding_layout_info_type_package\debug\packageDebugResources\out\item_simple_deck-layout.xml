<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_simple_deck" modulePackage="com.erroranalysis.app" filePath="app\src\main\res\layout\item_simple_deck.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_simple_deck_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="68" endOffset="51"/></Target><Target id="@+id/card_cover" view="FrameLayout"><Expressions/><location startLine="16" startOffset="8" endLine="35" endOffset="21"/></Target><Target id="@+id/text_cover_icon" view="TextView"><Expressions/><location startLine="22" startOffset="12" endLine="33" endOffset="42"/></Target><Target id="@+id/text_deck_name" view="TextView"><Expressions/><location startLine="44" startOffset="12" endLine="53" endOffset="42"/></Target><Target id="@+id/text_card_count" view="TextView"><Expressions/><location startLine="55" startOffset="12" endLine="62" endOffset="41"/></Target></Targets></Layout>