// Generated by view binder compiler. Do not edit!
package com.erroranalysis.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.erroranalysis.app.R;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogSimpleCreateDeckBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextInputEditText editDeckName;

  @NonNull
  public final TextInputEditText editSubject;

  @NonNull
  public final RecyclerView recyclerIcons;

  private DialogSimpleCreateDeckBinding(@NonNull LinearLayout rootView,
      @NonNull TextInputEditText editDeckName, @NonNull TextInputEditText editSubject,
      @NonNull RecyclerView recyclerIcons) {
    this.rootView = rootView;
    this.editDeckName = editDeckName;
    this.editSubject = editSubject;
    this.recyclerIcons = recyclerIcons;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogSimpleCreateDeckBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogSimpleCreateDeckBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_simple_create_deck, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogSimpleCreateDeckBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.edit_deck_name;
      TextInputEditText editDeckName = ViewBindings.findChildViewById(rootView, id);
      if (editDeckName == null) {
        break missingId;
      }

      id = R.id.edit_subject;
      TextInputEditText editSubject = ViewBindings.findChildViewById(rootView, id);
      if (editSubject == null) {
        break missingId;
      }

      id = R.id.recycler_icons;
      RecyclerView recyclerIcons = ViewBindings.findChildViewById(rootView, id);
      if (recyclerIcons == null) {
        break missingId;
      }

      return new DialogSimpleCreateDeckBinding((LinearLayout) rootView, editDeckName, editSubject,
          recyclerIcons);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
